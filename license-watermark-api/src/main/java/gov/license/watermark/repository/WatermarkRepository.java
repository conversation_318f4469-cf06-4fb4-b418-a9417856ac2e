package gov.license.watermark.repository;

import gov.license.common.api.repository.BaseRepository;
import gov.license.watermark.entity.WatermarkDo;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * 水印样式 持久化
 *
 * <AUTHOR>
 * @date 2023/3/17
 */
@Repository
public interface WatermarkRepository extends BaseRepository<WatermarkDo, String> {

    /**
     * 根据水印样式名称查询水印
     *
     * @param name 水印样式名称
     * @return 水印样式optional
     **/
    Optional<WatermarkDo> findByName(String name);
}
