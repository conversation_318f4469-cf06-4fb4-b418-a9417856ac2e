package gov.license.watermark.validate;

import gov.license.common.api.utils.Assert;
import gov.license.watermark.enums.PaperSizeEnum;
import gov.license.watermark.exception.WatermarkException;
import gov.license.watermark.req.WatermarkRequest;
import gov.license.watermark.resp.WatermarkResponseCode;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * 水印 请求参数校验
 *
 * <AUTHOR>
 * @date 2023/3/17
 */
public class WatermarkRequestParamValidate {

    /**
     * 创建水印 请求参数校验
     *
     * @param request 请求对象
     * @date 18:09 2023/3/17
     **/
    public static void createParamValidate(WatermarkRequest request) {
        // 判空
        Assert.hasText(request.getName(), new WatermarkException(WatermarkResponseCode.EDIT_LICENSE_WATERMARK_NAME_NULL));
        Assert.hasText(request.getDescription(), new WatermarkException(WatermarkResponseCode.EDIT_LICENSE_WATERMARK_DESCRIPTION_NULL));
        Assert.hasText(request.getWrapWay(), new WatermarkException(WatermarkResponseCode.EDIT_LICENSE_WATERMARK_WRAP_WAY_NULL));
        Assert.hasText(request.getFontFamily(), new WatermarkException(WatermarkResponseCode.EDIT_LICENSE_WATERMARK_FONT_FAMILY_NULL));
        Assert.hasText(request.getFontColor(), new WatermarkException(WatermarkResponseCode.EDIT_LICENSE_WATERMARK_FONT_COLOR_NULL));
        Assert.notNull(request.getFontSize(), new WatermarkException(WatermarkResponseCode.EDIT_LICENSE_WATERMARK_FONT_SIZE_NULL));
        Assert.notNull(request.getRotate(), new WatermarkException(WatermarkResponseCode.EDIT_LICENSE_WATERMARK_ROTATE_NULL));
        Assert.notNull(request.getPellucidity(), new WatermarkException(WatermarkResponseCode.EDIT_LICENSE_WATERMARK_PELLUCIDITY_NULL));
        Assert.notNull(request.getAxisX(), new WatermarkException(WatermarkResponseCode.EDIT_LICENSE_WATERMARK_AXIS_X_NULL));
        Assert.notNull(request.getAxisY(), new WatermarkException(WatermarkResponseCode.EDIT_LICENSE_WATERMARK_AXIS_Y_NULL));
        Assert.notNull(request.getStagger(), new WatermarkException(WatermarkResponseCode.EDIT_LICENSE_WATERMARK_STAGGER_NULL));
        if (String.valueOf(PaperSizeEnum.SELF.getValue()).equals(request.getPaperSize())) {
            Assert.notNull(request.getPaperUrx(), new WatermarkException(WatermarkResponseCode.EDIT_LICENSE_WATERMARK_PAPER_WIDTH_OR_HEIGHT_NULL));
            Assert.notNull(request.getPaperUry(), new WatermarkException(WatermarkResponseCode.EDIT_LICENSE_WATERMARK_PAPER_WIDTH_OR_HEIGHT_NULL));
        }

        // 取值范围
        Assert.isTrue(request.getName().length() <= 30, new WatermarkException(WatermarkResponseCode.EDIT_LICENSE_WATERMARK_NAME_TOO_LONG));
        Assert.isTrue(request.getDescription().length() <= 60, new WatermarkException(WatermarkResponseCode.EDIT_LICENSE_WATERMARK_DESCRIPTION_TOO_LONG));
        Assert.isTrue(request.getContent().length() <= 200, new WatermarkException(WatermarkResponseCode.EDIT_LICENSE_WATERMARK_CONTENT_TOO_LONG));
        Assert.isTrue(6 <= request.getFontSize() && request.getFontSize() <= 72, new WatermarkException(WatermarkResponseCode.EDIT_LICENSE_WATERMARK_FONT_SIZE_WRONG_RANGE));
        Assert.isTrue(0 <= request.getRotate() && request.getRotate() <= 360, new WatermarkException(WatermarkResponseCode.EDIT_LICENSE_WATERMARK_ROTATE_WRONG_RANGE));
        Assert.isTrue(0 <= request.getPellucidity() && request.getPellucidity() <= 100, new WatermarkException(WatermarkResponseCode.EDIT_LICENSE_WATERMARK_PELLUCIDITY_WRONG_RANGE));
        Assert.isTrue(-5000 <= request.getAxisX() && request.getAxisX() <= 5000, new WatermarkException(WatermarkResponseCode.EDIT_LICENSE_WATERMARK_AXIS_X_WRONG_RANGE));
        Assert.isTrue(-5000 <= request.getAxisY() && request.getAxisY() <= 5000, new WatermarkException(WatermarkResponseCode.EDIT_LICENSE_WATERMARK_AXIS_Y_WRONG_RANGE));
        Assert.isTrue(0 <= request.getRepeatRow() && request.getRepeatRow() <= 20, new WatermarkException(WatermarkResponseCode.EDIT_LICENSE_WATERMARK_REPEAT_ROW_WRONG_RANGE));
        Assert.isTrue(0 <= request.getRepeatColumn() && request.getRepeatColumn() <= 20, new WatermarkException(WatermarkResponseCode.EDIT_LICENSE_WATERMARK_REPEAT_COLUMN_WRONG_RANGE));
        Assert.isTrue(0 <= request.getSpacingX() && request.getSpacingX() <= 1000, new WatermarkException(WatermarkResponseCode.EDIT_LICENSE_WATERMARK_SPACING_X_WRONG_RANGE));
        Assert.isTrue(0 <= request.getSpacingY() && request.getSpacingY() <= 1000, new WatermarkException(WatermarkResponseCode.EDIT_LICENSE_WATERMARK_SPACING_Y_WRONG_RANGE));
    }

    /**
     * 删除水印 请求参数校验
     *
     * @param requestList 请求对象 集合
     * @date 13:27 2023/3/20
     **/
    public static void deleteInBatchValidate(List<WatermarkRequest> requestList) {
        Assert.isTrue(CollectionUtils.isNotEmpty(requestList), new WatermarkException(WatermarkResponseCode.DELETE_LICENSE_WATERMARK_LIST_NULL));
    }
}
