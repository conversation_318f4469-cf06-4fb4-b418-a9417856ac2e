package gov.license.watermark.validate;

import gov.license.common.api.utils.Assert;
import gov.license.watermark.exception.WatermarkException;
import gov.license.watermark.req.WatermarkItemRequest;
import gov.license.watermark.resp.WatermarkResponseCode;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * 水印内容信息项 请求参数校验
 *
 * <AUTHOR>
 * @date 2023/3/17
 */
public class WatermarkItemRequestParamValidate {

    /**
     * 创建水印内容信息项 请求参数校验
     *
     * @param requestList 请求对象 集合
     * @date 18:09 2023/3/17
     **/
    public static void createParamValidate(List<WatermarkItemRequest> requestList) {
        Assert.isTrue(CollectionUtils.isNotEmpty(requestList), new WatermarkException(WatermarkResponseCode.EDIT_LICENSE_WATERMARK_ITEM_LIST_NULL));
        requestList.stream().forEach(request -> Assert.hasText(request.getName(), new WatermarkException(WatermarkResponseCode.EDIT_LICENSE_WATERMARK_ITEM_NAME_NULL)));
    }

    /**
     * 删除水印内容信息项 请求参数校验
     *
     * @param requestList 请求对象 集合
     * @date 13:27 2023/3/20
     **/
    public static void deleteInBatchValidate(List<WatermarkItemRequest> requestList) {
        Assert.isTrue(CollectionUtils.isNotEmpty(requestList), new WatermarkException(WatermarkResponseCode.DELETE_LICENSE_WATERMARK_ITEM_LIST_NULL));
        requestList.stream().forEach(request -> Assert.hasText(request.getId(), new WatermarkException(WatermarkResponseCode.QUERY_LICENSE_WATERMARK_ITEM_ID_NULL)));
    }
}
