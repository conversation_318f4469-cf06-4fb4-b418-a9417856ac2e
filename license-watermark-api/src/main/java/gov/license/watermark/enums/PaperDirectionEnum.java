package gov.license.watermark.enums;

/**
 * 文档方向枚举
 *
 * <AUTHOR>
 * @date 2023/3/24
 */
public enum PaperDirectionEnum {

    /**
     * 横向
     */
    CROSSRANGE(0, "横向"),
    /**
     * 竖向
     */
    VERTICAL(1, "竖向");

    /**
     * 枚举值
     */
    private int value;
    /**
     * 尺寸名称
     */
    private String name;

    PaperDirectionEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    /**
     * 根据枚举值查询
     *
     * @param value 枚举值
     * @date 11:53 2023/3/24
     **/
    public static PaperDirectionEnum getPaperDirectionByValue(int value) {
        return PaperDirectionEnum.VERTICAL.getValue() == value ? PaperDirectionEnum.VERTICAL : PaperDirectionEnum.CROSSRANGE;
    }
}
