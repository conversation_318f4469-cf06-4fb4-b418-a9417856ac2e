package gov.license.watermark.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 字体
 *
 * <AUTHOR>
 * @date 2023/3/21
 */
public enum FontFamilyEnum {

    /**
     * 微软雅黑
     */
    MSYH(0, "微软雅黑", "msyh.ttf"),
    /**
     * 仿宋
     */
    SIMFANG(1, "仿宋", "simfang.ttf"),
    /**
     * 黑体
     */
    SIMHEI(2, "黑体", "simhei.ttf"),
    /**
     * 楷体
     */
    SIMKAI(3, "楷体", "simkai.ttf"),
    /**
     * 宋体
     */
    SIMSUN(4, "宋体", "simsun.ttf");

    /**
     * 枚举值
     */
    private int value;
    /**
     * 字体名称
     */
    private String name;
    /**
     * 字体文件名称
     */
    private String ttfName;

    FontFamilyEnum(int value, String name, String ttfName) {
        this.value = value;
        this.name = name;
        this.ttfName = ttfName;
    }

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getTtfName() {
        return ttfName;
    }

    public void setTtfName(String ttfName) {
        this.ttfName = ttfName;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    /**
     * 根据枚举值查询（默认微软雅黑）
     *
     * @param value 枚举值
     * @date 16:16 2023/3/21
     **/
    public static FontFamilyEnum getFontFamilyByValue(int value) {
        List<FontFamilyEnum> filter = Arrays.stream(values()).filter(fontFamilyEnum -> value == fontFamilyEnum.getValue()).collect(Collectors.toList());
        return filter.isEmpty() ? FontFamilyEnum.MSYH : filter.get(0);
    }

    /**
     * 根据字体名称查询（默认微软雅黑）
     *
     * @param name 字体名称
     * @date 16:16 2023/3/21
     **/
    public static FontFamilyEnum getFontFamilyByName(String name) {
        if (StringUtils.isBlank(name)) {
            return FontFamilyEnum.MSYH;
        }
        List<FontFamilyEnum> filter = Arrays.stream(values()).filter(fontFamilyEnum -> fontFamilyEnum.getName().equals(name)).collect(Collectors.toList());
        return filter.isEmpty() ? FontFamilyEnum.MSYH : filter.get(0);
    }
}
