package gov.license.watermark.enums;

/**
 * 水印操作日志类型
 *
 * <AUTHOR>
 * @date 2024/5/13
 */
public enum WatermarkLogTypeEnum {

    /**
     * 水印组件 -> 查询水印样式列表
     */
    WATERMARK_PAGE("水印样式管理", "查询水印样式列表"),
    /**
     * 水印样式 -> 查看水印样式详情
     */
    WATERMARK_VIEW("水印样式管理", "查看水印样式详情"),
    /**
     * 水印样式 -> 新增水印样式
     */
    WATERMARK_CREATE("水印样式管理", "新增水印样式"),
    /**
     * 水印样式 -> 编辑水印样式
     */
    WATERMARK_EDIT("水印样式管理", "编辑水印样式"),
    /**
     * 水印样式 -> 删除（批量）水印样式
     */
    WATERMARK_DELETE("水印样式管理", "删除（批量）水印样式"),
    /**
     * 水印样式 -> 设置默认水印
     */
    WATERMARK_SET_DEFAULT_FLAG("水印样式管理", "设置默认水印"),
    /**
     * 水印内容信息项 -> 查看水印内容信息项列表
     */
    WATERMARK_ITEM_LIST("水印样式管理", "查看水印内容信息项列表"),
    /**
     * 水印内容信息项 -> 创建水印内容信息项
     */
    WATERMARK_ITEM_CREATE("水印样式管理", "创建水印内容信息项"),
    /**
     * 水印内容信息项 -> 删除（批量）水印内容信息项
     */
    WATERMARK_ITEM_DELETE("水印样式管理", "删除（批量）水印内容信息项");

    WatermarkLogTypeEnum(String module, String event) {
        this.module = module;
        this.event = event;
    }

    /**
     * 模块
     */
    private String module;
    /**
     * 事件
     */
    private String event;

    public String getModule() {
        return module;
    }

    public void setModule(String module) {
        this.module = module;
    }

    public String getEvent() {
        return event;
    }

    public void setEvent(String event) {
        this.event = event;
    }
}
