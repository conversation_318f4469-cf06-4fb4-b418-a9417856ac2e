package gov.license.watermark.enums;

import com.itextpdf.text.PageSize;
import com.itextpdf.text.Rectangle;
import com.itextpdf.text.RectangleReadOnly;
import gov.license.common.api.utils.Assert;
import gov.license.watermark.exception.WatermarkException;
import gov.license.watermark.resp.WatermarkResponseCode;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 文档尺寸
 *
 * <AUTHOR>
 * @date 2023/3/23
 */
public enum PaperSizeEnum {

    /**
     * A3
     */
    A3(0, "A3", PageSize.A3),
    /**
     * A4
     */
    A4(1, "A4", PageSize.A4),
    /**
     * A5
     */
    A5(2, "A5", PageSize.A5),
    /**
     * A6
     */
    A6(3, "A6", PageSize.A6),
    /**
     * B3
     */
    B3(4, "B3", PageSize.B3),
    /**
     * B4
     */
    B4(5, "B4", PageSize.B4),
    /**
     * B5
     */
    B5(6, "B5", PageSize.B5),
    /**
     * C4
     */
    C4(7, "C4", new RectangleReadOnly(648.0F, 918.0F)),
    /**
     * C5
     */
    C5(8, "C5", new RectangleReadOnly(459.0F, 648.0F)),
    /**
     * C6
     */
    C6(9, "C6", new RectangleReadOnly(331.0F, 459.0F)),
    /**
     * SELF
     */
    SELF(10, "SELF", null),
    /**
     * A7
     */
    A7(11, "A7", PageSize.A7),
    /**
     * A8
     */
    A8(12, "A8", PageSize.A8),
    /**
     * A9
     */
    A9(13, "A9", PageSize.A9),
    /**
     * A10
     */
    A10(14, "A10", PageSize.A10);

    /**
     * 枚举值
     */
    private int value;
    /**
     * 尺寸名称
     */
    private String name;
    /**
     * 尺寸类型
     */
    private Rectangle rectangle;

    PaperSizeEnum(int value, String name, Rectangle rectangle) {
        this.value = value;
        this.name = name;
        this.rectangle = rectangle;
    }

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Rectangle getRectangle() {
        return rectangle;
    }

    public void setRectangle(Rectangle rectangle) {
        this.rectangle = rectangle;
    }

    /**
     * 根据枚举值查询
     *
     * @param value 枚举值
     * @date 11:53 2023/3/23
     **/
    public static PaperSizeEnum getPaperSizeByValue(int value) {
        List<PaperSizeEnum> filter = Arrays.stream(values()).filter(paperSizeEnum -> value == paperSizeEnum.getValue()).collect(Collectors.toList());
        Assert.notEmpty(filter, new WatermarkException(WatermarkResponseCode.PDF_PAPERSIZE_ERROR));
        return filter.get(0);
    }

    /**
     * 根据尺寸名称查询
     *
     * @param name 尺寸名称
     * @date 11:53 2023/3/23
     **/
    public static PaperSizeEnum getPaperSizeByName(String name) {
        Assert.hasText(name, new WatermarkException(WatermarkResponseCode.PDF_PAPERSIZE_ERROR));
        List<PaperSizeEnum> filter = Arrays.stream(values()).filter(paperSizeEnum -> paperSizeEnum.getName().equals(name)).collect(Collectors.toList());
        Assert.notEmpty(filter, new WatermarkException(WatermarkResponseCode.PDF_PAPERSIZE_ERROR));
        return filter.get(0);
    }
}
