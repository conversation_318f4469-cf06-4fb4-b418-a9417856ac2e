package gov.license.watermark.enums;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 换行方式枚举
 *
 * <AUTHOR>
 * @date 2023/3/27
 */
public enum WrapWayEnum {

    /**
     * 不换行
     */
    NONE(0, "不换行"),
    /**
     * 自动换行
     */
    AUTO(1, "自动换行"),
    /**
     * 限定字数换行
     */
    WORD_LIMITED(2, "限定字数换行"),
    /**
     * 关键字换行
     */
    KEYWORD(3, "关键字换行"),
    /**
     * 换行符换行
     */
    LINE_BREAK(4, "换行符换行");

    /**
     * 枚举值
     */
    private int value;
    /**
     * 尺寸名称
     */
    private String name;

    WrapWayEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    /**
     * 根据枚举值查询
     *
     * @param value 枚举值
     * @date 15:13 2023/3/27
     **/
    public static WrapWayEnum getWrapWayEnumByValue(int value) {
        List<WrapWayEnum> filter = Arrays.stream(values()).filter(wrapWayEnum -> value == wrapWayEnum.getValue()).collect(Collectors.toList());
        return filter.isEmpty() ? WrapWayEnum.AUTO : filter.get(0);
    }
}
