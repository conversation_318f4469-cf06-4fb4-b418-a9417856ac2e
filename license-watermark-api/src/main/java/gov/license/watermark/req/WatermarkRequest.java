package gov.license.watermark.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import gov.license.common.api.req.BasePageRequest;

/**
 * 水印样式对象请求体
 *
 * <AUTHOR>
 * @date 2023/3/16
 */
public class WatermarkRequest extends BasePageRequest {
    private static final long serialVersionUID = 3882617108852577238L;

    /**
     * 唯一主键
     */
    private String id;
    /**
     * 样式名称
     */
    private String name;
    /**
     * 应用说明
     */
    private String description;
    /**
     * 默认水印内容
     */
    private String content;
    /**
     * 换行方式
     */
    @JsonProperty(value = "wrap_way")
    private String wrapWay;
    /**
     * 换行值
     */
    @JsonProperty(value = "wrap_value")
    private String wrapValue;
    /**
     * 文字字体
     */
    @JsonProperty(value = "font_family")
    private String fontFamily;
    /**
     * 文字颜色
     */
    @JsonProperty(value = "font_color")
    private String fontColor;
    /**
     * 文字大小
     */
    @JsonProperty(value = "font_size")
    private Integer fontSize;
    /**
     * 旋转角度
     */
    private Integer rotate;
    /**
     * 透明度
     */
    private Integer pellucidity;
    /**
     * 水印坐标X轴
     */
    @JsonProperty(value = "axis_x")
    private Integer axisX;
    /**
     * 水印坐标Y轴
     */
    @JsonProperty(value = "axis_y")
    private Integer axisY;
    /**
     * 水印重复-行
     */
    @JsonProperty(value = "repeat_row")
    private Integer repeatRow;
    /**
     * 水印重复-列
     */
    @JsonProperty(value = "repeat_column")
    private Integer repeatColumn;
    /**
     * 是否平铺
     */
    private Boolean tiled;
    /**
     * 间距X轴
     */
    @JsonProperty(value = "spacing_x")
    private Integer spacingX;
    /**
     * 间距Y轴
     */
    @JsonProperty(value = "spacing_y")
    private Integer spacingY;
    /**
     * 水印交错
     */
    private Integer stagger;
    /**
     * 文档尺寸
     */
    @JsonProperty(value = "paper_size")
    private String paperSize;
    /**
     * 文档方向
     */
    @JsonProperty(value = "paper_direction")
    private String paperDirection;
    /**
     * 尺寸自定义宽度
     */
    @JsonProperty(value = "paper_urx")
    private Integer paperUrx;
    /**
     * 尺寸自定义高度
     */
    @JsonProperty(value = "paper_ury")
    private Integer paperUry;
    /**
     * 默认水印标识
     */
    @JsonProperty(value = "default_flag")
    private boolean defaultFlag;
    /**
     * 根据页面大小自动缩小
     */
    @JsonProperty(value = "auto_resize")
    private boolean autoResize;
    /**
     * 根据页面斜边自动设置倾斜角度
     */
    @JsonProperty(value = "auto_calc_gradient")
    private boolean autoCalcGradient;

    public WatermarkRequest() {
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getWrapWay() {
        return wrapWay;
    }

    public void setWrapWay(String wrapWay) {
        this.wrapWay = wrapWay;
    }

    public String getWrapValue() {
        return wrapValue;
    }

    public void setWrapValue(String wrapValue) {
        this.wrapValue = wrapValue;
    }

    public String getFontFamily() {
        return fontFamily;
    }

    public void setFontFamily(String fontFamily) {
        this.fontFamily = fontFamily;
    }

    public String getFontColor() {
        return fontColor;
    }

    public void setFontColor(String fontColor) {
        this.fontColor = fontColor;
    }

    public Integer getFontSize() {
        return fontSize;
    }

    public void setFontSize(Integer fontSize) {
        this.fontSize = fontSize;
    }

    public Integer getRotate() {
        return rotate;
    }

    public void setRotate(Integer rotate) {
        this.rotate = rotate;
    }

    public Integer getPellucidity() {
        return pellucidity;
    }

    public void setPellucidity(Integer pellucidity) {
        this.pellucidity = pellucidity;
    }

    public Integer getAxisX() {
        return axisX;
    }

    public void setAxisX(Integer axisX) {
        this.axisX = axisX;
    }

    public Integer getAxisY() {
        return axisY;
    }

    public void setAxisY(Integer axisY) {
        this.axisY = axisY;
    }

    public Integer getRepeatRow() {
        return repeatRow;
    }

    public void setRepeatRow(Integer repeatRow) {
        this.repeatRow = repeatRow;
    }

    public Integer getRepeatColumn() {
        return repeatColumn;
    }

    public void setRepeatColumn(Integer repeatColumn) {
        this.repeatColumn = repeatColumn;
    }

    public Boolean getTiled() {
        return tiled;
    }

    public void setTiled(Boolean tiled) {
        this.tiled = tiled;
    }

    public Integer getSpacingX() {
        return spacingX;
    }

    public void setSpacingX(Integer spacingX) {
        this.spacingX = spacingX;
    }

    public Integer getSpacingY() {
        return spacingY;
    }

    public void setSpacingY(Integer spacingY) {
        this.spacingY = spacingY;
    }

    public Integer getStagger() {
        return stagger;
    }

    public void setStagger(Integer stagger) {
        this.stagger = stagger;
    }

    public String getPaperSize() {
        return paperSize;
    }

    public void setPaperSize(String paperSize) {
        this.paperSize = paperSize;
    }

    public String getPaperDirection() {
        return paperDirection;
    }

    public void setPaperDirection(String paperDirection) {
        this.paperDirection = paperDirection;
    }

    public Integer getPaperUrx() {
        return paperUrx;
    }

    public void setPaperUrx(Integer paperUrx) {
        this.paperUrx = paperUrx;
    }

    public Integer getPaperUry() {
        return paperUry;
    }

    public void setPaperUry(Integer paperUry) {
        this.paperUry = paperUry;
    }

    public Boolean getDefaultFlag() {
        return defaultFlag;
    }

    public void setDefaultFlag(Boolean defaultFlag) {
        this.defaultFlag = defaultFlag;
    }

    public Boolean getAutoResize() {
        return autoResize;
    }

    public void setAutoResize(Boolean autoResize) {
        this.autoResize = autoResize;
    }

    public Boolean getAutoCalcGradient() {
        return autoCalcGradient;
    }

    public void setAutoCalcGradient(Boolean autoCalcGradient) {
        this.autoCalcGradient = autoCalcGradient;
    }

    @Override
    public String toString() {
        return "WatermarkRequest{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", content='" + content + '\'' +
                ", wrapWay='" + wrapWay + '\'' +
                ", wrapValue='" + wrapValue + '\'' +
                ", fontFamily='" + fontFamily + '\'' +
                ", fontColor='" + fontColor + '\'' +
                ", fontSize=" + fontSize +
                ", rotate=" + rotate +
                ", pellucidity=" + pellucidity +
                ", axisX=" + axisX +
                ", axisY=" + axisY +
                ", repeatRow=" + repeatRow +
                ", repeatColumn=" + repeatColumn +
                ", tiled=" + tiled +
                ", spacingX=" + spacingX +
                ", spacingY=" + spacingY +
                ", stagger=" + stagger +
                ", paperSize='" + paperSize + '\'' +
                ", paperDirection='" + paperDirection + '\'' +
                ", paperUrx=" + paperUrx +
                ", paperUry=" + paperUry +
                ", defaultFlag=" + defaultFlag +
                ", autoResize=" + autoResize +
                ", autoCalcGradient=" + autoCalcGradient +
                "} " + super.toString();
    }
}
