package gov.license.watermark.req;

import gov.license.common.api.req.BaseRequest;

/**
 * 水印内容信息项请求体
 *
 * <AUTHOR>
 * @date 2023/3/27
 */
public class WatermarkItemRequest extends BaseRequest {
    private static final long serialVersionUID = 3184125037821798498L;

    /**
     * 唯一主键
     */
    private String id;
    /**
     * 名称
     */
    private String name;
    /**
     * 排序
     */
    private Integer sort;

    public WatermarkItemRequest() {
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }
}
