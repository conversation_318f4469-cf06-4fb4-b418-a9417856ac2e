package gov.license.watermark.entity;

import gov.license.common.api.entity.BaseDo;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 水印内容信息项
 *
 * <AUTHOR>
 * @date 2023/3/27
 */
@Entity
@Table(name = "TBL_WATERMARK_ITEM")
public class WatermarkItemDo extends BaseDo {
    private static final long serialVersionUID = 3184125037821798498L;

    /**
     * 名称
     */
    @Column(name = "NAME")
    private String name;
    /**
     * 排序
     */
    @Column(name = "SORT")
    private Integer sort;

    public WatermarkItemDo() {
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    @Override
    public String toString() {
        return "WatermarkItemDo{" +
                "name='" + name + '\'' +
                ", sort=" + sort +
                "} " + super.toString();
    }
}
