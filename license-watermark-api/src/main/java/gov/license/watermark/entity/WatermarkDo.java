package gov.license.watermark.entity;

import gov.license.common.api.entity.BaseDo;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 水印样式对象
 *
 * <AUTHOR>
 * @date 2023/3/16
 */
@Entity
@Table(name = "TBL_WATERMARK")
public class WatermarkDo extends BaseDo {
    private static final long serialVersionUID = 3882617108852577238L;

    /**
     * 样式名称（必填）
     */
    @Column(name = "NAME")
    private String name;
    /**
     * 应用说明（必填）
     */
    @Column(name = "DESCRIPTION")
    private String description;
    /**
     * 默认水印内容
     */
    @Column(name = "CONTENT")
    private String content;
    /**
     * 换行方式（必填）
     */
    @Column(name = "WRAP_WAY")
    private String wrapWay;
    /**
     * 换行值
     */
    @Column(name = "WRAP_VALUE")
    private String wrapValue;
    /**
     * 文字字体（必填）
     */
    @Column(name = "FONT_FAMILY")
    private String fontFamily;
    /**
     * 文字颜色（必填）
     */
    @Column(name = "FONT_COLOR")
    private String fontColor;
    /**
     * 文字大小（必填）
     */
    @Column(name = "FONT_SIZE")
    private Integer fontSize;
    /**
     * 旋转角度（必填）
     */
    @Column(name = "ROTATE")
    private Integer rotate;
    /**
     * 透明度（必填）
     */
    @Column(name = "PELLUCIDITY")
    private Integer pellucidity;
    /**
     * 水印坐标X轴（必填）
     */
    @Column(name = "AXIS_X")
    private Integer axisX;
    /**
     * 水印坐标Y轴（必填）
     */
    @Column(name = "AXIS_Y")
    private Integer axisY;
    /**
     * 水印重复-行
     */
    @Column(name = "REPEAT_ROW")
    private Integer repeatRow;
    /**
     * 水印重复-列
     */
    @Column(name = "REPEAT_COLUMN")
    private Integer repeatColumn;
    /**
     * 是否平铺
     */
    @Column(name = "TILED")
    private Boolean tiled;
    /**
     * 间距X轴
     */
    @Column(name = "SPACING_X")
    private Integer spacingX;
    /**
     * 间距Y轴
     */
    @Column(name = "SPACING_Y")
    private Integer spacingY;
    /**
     * 水印交错（必填）
     */
    @Column(name = "STAGGER")
    private Integer stagger;
    /**
     * 文档尺寸
     */
    @Column(name = "PAPER_SIZE")
    private String paperSize;
    /**
     * 文档方向
     */
    @Column(name = "PAPER_DIRECTION")
    private String paperDirection;
    /**
     * 尺寸自定义宽度
     */
    @Column(name = "PAPER_URX")
    private Integer paperUrx;
    /**
     * 尺寸自定义高度
     */
    @Column(name = "PAPER_URY")
    private Integer paperUry;
    /**
     * 默认水印标识
     */
    @Column(name = "DEFAULT_FLAG")
    private Boolean defaultFlag;
    /**
     * 根据页面大小自动缩小
     */
    @Column(name = "AUTO_RESIZE")
    private Boolean autoResize;
    /**
     * 根据页面斜边自动设置倾斜角度
     */
    @Column(name = "AUTO_CALC_GRADIENT")
    private Boolean autoCalcGradient;

    public WatermarkDo() {
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getWrapWay() {
        return wrapWay;
    }

    public void setWrapWay(String wrapWay) {
        this.wrapWay = wrapWay;
    }

    public String getWrapValue() {
        return wrapValue;
    }

    public void setWrapValue(String wrapValue) {
        this.wrapValue = wrapValue;
    }

    public String getFontFamily() {
        return fontFamily;
    }

    public void setFontFamily(String fontFamily) {
        this.fontFamily = fontFamily;
    }

    public String getFontColor() {
        return fontColor;
    }

    public void setFontColor(String fontColor) {
        this.fontColor = fontColor;
    }

    public Integer getFontSize() {
        return fontSize;
    }

    public void setFontSize(Integer fontSize) {
        this.fontSize = fontSize;
    }

    public Integer getRotate() {
        return rotate;
    }

    public void setRotate(Integer rotate) {
        this.rotate = rotate;
    }

    public Integer getPellucidity() {
        return pellucidity;
    }

    public void setPellucidity(Integer pellucidity) {
        this.pellucidity = pellucidity;
    }

    public Integer getAxisX() {
        return axisX;
    }

    public void setAxisX(Integer axisX) {
        this.axisX = axisX;
    }

    public Integer getAxisY() {
        return axisY;
    }

    public void setAxisY(Integer axisY) {
        this.axisY = axisY;
    }

    public Integer getRepeatRow() {
        return repeatRow;
    }

    public void setRepeatRow(Integer repeatRow) {
        this.repeatRow = repeatRow;
    }

    public Integer getRepeatColumn() {
        return repeatColumn;
    }

    public void setRepeatColumn(Integer repeatColumn) {
        this.repeatColumn = repeatColumn;
    }

    public Boolean getTiled() {
        return tiled;
    }

    public void setTiled(Boolean tiled) {
        this.tiled = tiled;
    }

    public Integer getSpacingX() {
        return spacingX;
    }

    public void setSpacingX(Integer spacingX) {
        this.spacingX = spacingX;
    }

    public Integer getSpacingY() {
        return spacingY;
    }

    public void setSpacingY(Integer spacingY) {
        this.spacingY = spacingY;
    }

    public Integer getStagger() {
        return stagger;
    }

    public void setStagger(Integer stagger) {
        this.stagger = stagger;
    }

    public String getPaperSize() {
        return paperSize;
    }

    public void setPaperSize(String paperSize) {
        this.paperSize = paperSize;
    }

    public String getPaperDirection() {
        return paperDirection;
    }

    public void setPaperDirection(String paperDirection) {
        this.paperDirection = paperDirection;
    }

    public Integer getPaperUrx() {
        return paperUrx;
    }

    public void setPaperUrx(Integer paperUrx) {
        this.paperUrx = paperUrx;
    }

    public Integer getPaperUry() {
        return paperUry;
    }

    public void setPaperUry(Integer paperUry) {
        this.paperUry = paperUry;
    }

    public Boolean getDefaultFlag() {
        return defaultFlag;
    }

    public void setDefaultFlag(Boolean defaultFlag) {
        this.defaultFlag = defaultFlag;
    }

    public Boolean getAutoResize() {
        return autoResize;
    }

    public void setAutoResize(Boolean autoResize) {
        this.autoResize = autoResize;
    }

    public Boolean getAutoCalcGradient() {
        return autoCalcGradient;
    }

    public void setAutoCalcGradient(Boolean autoCalcGradient) {
        this.autoCalcGradient = autoCalcGradient;
    }

    @Override
    public String toString() {
        return "WatermarkDo{" +
                "name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", content='" + content + '\'' +
                ", wrapWay='" + wrapWay + '\'' +
                ", wrapValue='" + wrapValue + '\'' +
                ", fontFamily='" + fontFamily + '\'' +
                ", fontColor='" + fontColor + '\'' +
                ", fontSize=" + fontSize +
                ", rotate=" + rotate +
                ", pellucidity=" + pellucidity +
                ", axisX=" + axisX +
                ", axisY=" + axisY +
                ", repeatRow=" + repeatRow +
                ", repeatColumn=" + repeatColumn +
                ", tiled=" + tiled +
                ", spacingX=" + spacingX +
                ", spacingY=" + spacingY +
                ", stagger=" + stagger +
                ", paperSize='" + paperSize + '\'' +
                ", paperDirection='" + paperDirection + '\'' +
                ", paperUrx=" + paperUrx +
                ", paperUry=" + paperUry +
                ", defaultFlag=" + defaultFlag +
                ", autoResize=" + autoResize +
                ", autoCalcGradient=" + autoCalcGradient +
                "} " + super.toString();
    }
}
