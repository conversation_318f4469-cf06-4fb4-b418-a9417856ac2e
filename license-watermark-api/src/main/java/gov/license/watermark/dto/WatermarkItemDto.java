package gov.license.watermark.dto;

import gov.license.common.api.dto.BaseDto;

/**
 * 水印内容信息项
 *
 * <AUTHOR>
 * @date 2023/3/27
 */
public class WatermarkItemDto extends BaseDto {
    private static final long serialVersionUID = 3184125037821798498L;

    /**
     * 名称
     */
    private String name;
    /**
     * 排序
     */
    private Integer sort;

    public WatermarkItemDto() {
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    @Override
    public String toString() {
        return "WatermarkItemDto{" +
                "name='" + name + '\'' +
                ", sort=" + sort +
                "} " + super.toString();
    }
}
