package gov.license.watermark.dto;

import java.io.Serializable;

/**
 * 换行方式执行逻辑处理后返回dto
 *
 * <AUTHOR>
 * @date 2024/5/8
 */
public class WrapWayExecutioOutDto implements Serializable {
    private static final long serialVersionUID = 6874997346255618470L;

    /**
     * 输出的水印内容
     */
    private StringBuilder outputContent;

    public WrapWayExecutioOutDto() {
    }

    public WrapWayExecutioOutDto(StringBuilder outputContent) {
        this.outputContent = outputContent;
    }

    public StringBuilder getOutputContent() {
        return outputContent;
    }

    public void setOutputContent(StringBuilder outputContent) {
        this.outputContent = outputContent;
    }

    @Override
    public String toString() {
        return "WrapWayExecutioOutDto{" +
                "outputContent='" + outputContent.toString() + '\'' +
                '}';
    }
}
