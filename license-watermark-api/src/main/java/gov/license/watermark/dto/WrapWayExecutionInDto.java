package gov.license.watermark.dto;

import com.itextpdf.text.pdf.BaseFont;
import gov.license.watermark.enums.WrapWayEnum;

import java.io.Serializable;

/**
 * 换行方式执行逻辑输入dto
 *
 * <AUTHOR>
 * @date 2024/5/8
 */
public class WrapWayExecutionInDto implements Serializable {
    private static final long serialVersionUID = -8961143448009624390L;
    /**
     * 水印对象
     */
    private WatermarkDto watermarkDto;
    /**
     * 字体
     */
    private BaseFont base;
    /**
     * 文档宽度
     */
    private float pageRectWidth;
    /**
     * 换行方式
     */
    private WrapWayEnum wrapWay;
    /**
     * 已计算resize的标识
     */
    private boolean reCalcResizeFlag;

    public WrapWayExecutionInDto() {
    }

    public WatermarkDto getWatermarkDto() {
        return watermarkDto;
    }

    public void setWatermarkDto(WatermarkDto watermarkDto) {
        this.watermarkDto = watermarkDto;
    }

    public BaseFont getBase() {
        return base;
    }

    public void setBase(BaseFont base) {
        this.base = base;
    }

    public float getPageRectWidth() {
        return pageRectWidth;
    }

    public void setPageRectWidth(float pageRectWidth) {
        this.pageRectWidth = pageRectWidth;
    }

    public boolean getReCalcResizeFlag() {
        return reCalcResizeFlag;
    }

    public void setReCalcResizeFlag(boolean reCalcResizeFlag) {
        this.reCalcResizeFlag = reCalcResizeFlag;
    }

    public WrapWayEnum getWrapWay() {
        return wrapWay;
    }

    public void setWrapWay(WrapWayEnum wrapWay) {
        this.wrapWay = wrapWay;
    }

    @Override
    public String toString() {
        return "WrapWayExecutionInDto{" +
                "watermarkDto=" + watermarkDto +
                ", base=" + base +
                ", pageRectWidth=" + pageRectWidth +
                ", wrapWay=" + wrapWay +
                ", reCalcResizeFlag=" + reCalcResizeFlag +
                '}';
    }
}
