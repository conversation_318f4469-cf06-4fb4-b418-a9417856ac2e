package gov.license.watermark.resp;

import gov.license.common.api.resp.BaseResponseCode;

/**
 * 水印 响应代码类
 * 第一个位置(数字 1位) - 错误级别，'系统级别' 固定值 [1]，'服务(业务)级别' 固定值 [2]，'参数级别' 固定值 [3]
 * 第二个位置(数字 2位) - 子系统，本水印组件固定00
 * 第三个位置(数字 2位) - 模块名称，本水印组件固定01
 * 第四个位置(数字 2位) - 动作
 * 第五个位置(数字 3位) - 返回码序号
 *
 * <AUTHOR>
 * @date 2023/3/17
 */
public class WatermarkResponseCode extends BaseResponseCode {

    /**
     * 水印业务 2000101***
     */
    public static final WatermarkResponseCode GENERATE_LICENSE_WATERMARK_ERROR = new WatermarkResponseCode("**********",
            "生成水印异常");

    /**
     * 水印查询 3000101***
     */
    public static final WatermarkResponseCode QUERY_LICENSE_WATERMARK_ERROR = new WatermarkResponseCode("**********",
            "水印查询异常");
    public static final WatermarkResponseCode QUERY_LICENSE_WATERMARK_ID_NULL = new WatermarkResponseCode("**********",
            "唯一标识不能为空");
    public static final WatermarkResponseCode QUERY_LICENSE_WATERMARK_NOT_EXIST = new WatermarkResponseCode("**********",
            "水印信息不存在");
    public static final WatermarkResponseCode QUERY_LICENSE_WATERMARK_NAME_EXIST = new WatermarkResponseCode("**********",
            "样式名称已存在");
    public static final WatermarkResponseCode QUERY_DEFAULT_WATERMARK_COUNT_ERROR = new WatermarkResponseCode("**********",
            "默认水印应当只有一个");
    public static final WatermarkResponseCode NO_DEFAULT_WATERMARK = new WatermarkResponseCode("**********",
            "无默认水印样式，请指定一种样式为默认水印");

    /**
     * 水印创建 3000102***
     */
    public static final WatermarkResponseCode EDIT_LICENSE_WATERMARK_ERROR = new WatermarkResponseCode("**********",
            "水印创建或编辑异常");
    public static final WatermarkResponseCode EDIT_LICENSE_WATERMARK_ID_NULL = new WatermarkResponseCode("**********",
            "唯一标识不能为空");
    public static final WatermarkResponseCode EDIT_LICENSE_WATERMARK_NAME_NULL = new WatermarkResponseCode("**********",
            "样式名称不能为空");
    public static final WatermarkResponseCode EDIT_LICENSE_WATERMARK_DESCRIPTION_NULL = new WatermarkResponseCode("**********",
            "应用说明不能为空");
    public static final WatermarkResponseCode EDIT_LICENSE_WATERMARK_WRAP_WAY_NULL = new WatermarkResponseCode("**********",
            "换行方式不能为空");
    public static final WatermarkResponseCode EDIT_LICENSE_WATERMARK_FONT_FAMILY_NULL = new WatermarkResponseCode("**********",
            "文字字体不能为空");
    public static final WatermarkResponseCode EDIT_LICENSE_WATERMARK_FONT_COLOR_NULL = new WatermarkResponseCode("**********",
            "文字颜色不能为空");
    public static final WatermarkResponseCode EDIT_LICENSE_WATERMARK_FONT_SIZE_NULL = new WatermarkResponseCode("**********",
            "文字大小不能为空");
    public static final WatermarkResponseCode EDIT_LICENSE_WATERMARK_ROTATE_NULL = new WatermarkResponseCode("**********",
            "旋转角度不能为空");
    public static final WatermarkResponseCode EDIT_LICENSE_WATERMARK_PELLUCIDITY_NULL = new WatermarkResponseCode("**********",
            "透明度不能为空");
    public static final WatermarkResponseCode EDIT_LICENSE_WATERMARK_AXIS_X_NULL = new WatermarkResponseCode("**********",
            "水印坐标X轴不能为空");
    public static final WatermarkResponseCode EDIT_LICENSE_WATERMARK_AXIS_Y_NULL = new WatermarkResponseCode("**********",
            "水印坐标Y轴不能为空");
    public static final WatermarkResponseCode EDIT_LICENSE_WATERMARK_STAGGER_NULL = new WatermarkResponseCode("**********",
            "水印交错不能为空");
    public static final WatermarkResponseCode EDIT_LICENSE_WATERMARK_PAPER_WIDTH_OR_HEIGHT_NULL = new WatermarkResponseCode("**********",
            "文档尺寸宽度或高度不能为空");


    public static final WatermarkResponseCode EDIT_LICENSE_WATERMARK_NAME_TOO_LONG = new WatermarkResponseCode("**********",
            "样式名称超过最大长度");
    public static final WatermarkResponseCode EDIT_LICENSE_WATERMARK_DESCRIPTION_TOO_LONG = new WatermarkResponseCode("**********",
            "应用说明超过最大长度");
    public static final WatermarkResponseCode EDIT_LICENSE_WATERMARK_CONTENT_TOO_LONG = new WatermarkResponseCode("**********",
            "水印内容超过最大长度");
    public static final WatermarkResponseCode EDIT_LICENSE_WATERMARK_FONT_SIZE_WRONG_RANGE = new WatermarkResponseCode("**********",
            "文字大小超出取值范围");
    public static final WatermarkResponseCode EDIT_LICENSE_WATERMARK_ROTATE_WRONG_RANGE = new WatermarkResponseCode("**********",
            "旋转角度超出取值范围");
    public static final WatermarkResponseCode EDIT_LICENSE_WATERMARK_PELLUCIDITY_WRONG_RANGE = new WatermarkResponseCode("**********",
            "透明度超出取值范围");
    public static final WatermarkResponseCode EDIT_LICENSE_WATERMARK_AXIS_X_WRONG_RANGE = new WatermarkResponseCode("**********",
            "水印X坐标超出取值范围");
    public static final WatermarkResponseCode EDIT_LICENSE_WATERMARK_AXIS_Y_WRONG_RANGE = new WatermarkResponseCode("**********",
            "水印Y坐标超出取值范围");
    public static final WatermarkResponseCode EDIT_LICENSE_WATERMARK_REPEAT_ROW_WRONG_RANGE = new WatermarkResponseCode("**********",
            "水印重复-行超出取值范围");
    public static final WatermarkResponseCode EDIT_LICENSE_WATERMARK_REPEAT_COLUMN_WRONG_RANGE = new WatermarkResponseCode("**********",
            "水印重复-列超出取值范围");
    public static final WatermarkResponseCode EDIT_LICENSE_WATERMARK_SPACING_X_WRONG_RANGE = new WatermarkResponseCode("**********",
            "水印X间距超出取值范围");
    public static final WatermarkResponseCode EDIT_LICENSE_WATERMARK_SPACING_Y_WRONG_RANGE = new WatermarkResponseCode("**********",
            "水印Y间距超出取值范围");

    /**
     * 水印删除 3000103***
     */
    public static final WatermarkResponseCode DELETE_LICENSE_WATERMARK_ERROR = new WatermarkResponseCode("**********",
            "水印删除异常");
    public static final WatermarkResponseCode DELETE_LICENSE_WATERMARK_LIST_NULL = new WatermarkResponseCode("**********",
            "水印对象集合不能为空");
    public static final WatermarkResponseCode DELETE_LICENSE_WATERMARK_ID_NULL = new WatermarkResponseCode("**********",
            "唯一标识不能为空");
    public static final WatermarkResponseCode DEFAULT_LICENSE_WATERMARK_CANNOT_DELETE = new WatermarkResponseCode("**********",
            "不能删除默认水印");

    /**
     * 水印内容信息项查询 3000104***
     */
    public static final WatermarkResponseCode QUERY_LICENSE_WATERMARK_ITEM_ERROR = new WatermarkResponseCode("**********",
            "水印内容信息项查询异常");
    public static final WatermarkResponseCode QUERY_LICENSE_WATERMARK_ITEM_ID_NULL = new WatermarkResponseCode("**********",
            "唯一标识不能为空");
    public static final WatermarkResponseCode QUERY_LICENSE_WATERMARK_ITEM_NOT_EXIST = new WatermarkResponseCode("**********",
            "水印内容信息项不存在");

    /**
     * 水印内容信息项创建 3000105***
     */
    public static final WatermarkResponseCode EDIT_LICENSE_WATERMARK_ITEM_ERROR = new WatermarkResponseCode("**********",
            "水印内容信息项创建异常");
    public static final WatermarkResponseCode EDIT_LICENSE_WATERMARK_ITEM_LIST_NULL = new WatermarkResponseCode("**********",
            "水印内容信息项对象集合不能为空");
    public static final WatermarkResponseCode EDIT_LICENSE_WATERMARK_ITEM_NAME_NULL = new WatermarkResponseCode("**********",
            "水印内容信息项名称不能为空");

    /**
     * 水印内容信息项删除 3000106***
     */
    public static final WatermarkResponseCode DELETE_LICENSE_WATERMARK_ITEM_ERROR = new WatermarkResponseCode("**********",
            "水印内容信息项删除异常");
    public static final WatermarkResponseCode DELETE_LICENSE_WATERMARK_ITEM_LIST_NULL = new WatermarkResponseCode("**********",
            "水印对象集合不能为空");
    public static final WatermarkResponseCode DELETE_LICENSE_WATERMARK_ITEM_ID_NULL = new WatermarkResponseCode("**********",
            "唯一标识不能为空");

    /**
     * 水印预览异常 3000107***
     */
    public static final WatermarkResponseCode GENERATE_WATERMARK_ERROR = new WatermarkResponseCode("**********",
            "水印预览生成异常");
    public static final WatermarkResponseCode GENERATE_WATERMARK_FONT_ERROR = new WatermarkResponseCode("**********",
            "字体文件读取异常");

    /**
     * pdf文档参数异常 3000108***
     */
    public static final WatermarkResponseCode PDF_PARAM_ERROR = new WatermarkResponseCode("**********",
            "pdf文档参数异常");
    public static final WatermarkResponseCode PDF_PAPERSIZE_ERROR = new WatermarkResponseCode("**********",
            "pdf文档尺寸参数异常");

    public WatermarkResponseCode(String code, String message) {
        super(code, message);
    }
}
