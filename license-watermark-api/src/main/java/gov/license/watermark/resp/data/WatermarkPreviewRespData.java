package gov.license.watermark.resp.data;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 水印预览返回体
 *
 * <AUTHOR>
 * @date 2023/3/27
 */
public class WatermarkPreviewRespData {
    /**
     * 模板唯一主键
     */
    private String id;
    /**
     * base64数据
     */
    @JsonProperty(value = "base64_data")
    private String base64Data;

    public WatermarkPreviewRespData() {
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getBase64Data() {
        return base64Data;
    }

    public void setBase64Data(String base64Data) {
        this.base64Data = base64Data;
    }
}
