package gov.license.watermark.resp.data;

import gov.license.common.api.resp.BaseResponse;

/**
 * 水印内容信息项返回体
 *
 * <AUTHOR>
 * @date 2023/3/27
 */
public class WatermarkItemRespData extends BaseResponse {
    private static final long serialVersionUID = 3184125037821798498L;

    /**
     * 唯一主键
     */
    private String id;
    /**
     * 名称
     */
    private String name;
    /**
     * 排序
     */
    private Integer sort;

    public WatermarkItemRespData() {
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }
}
