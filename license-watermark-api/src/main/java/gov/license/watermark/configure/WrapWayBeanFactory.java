package gov.license.watermark.configure;

import com.google.common.collect.Maps;
import com.itextpdf.text.pdf.BaseFont;
import gov.license.watermark.constants.WatermarkConstant;
import gov.license.watermark.dto.WrapWayExecutioOutDto;
import gov.license.watermark.dto.WrapWayExecutionInDto;
import gov.license.watermark.enums.WrapWayEnum;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 不同换行方式的处理逻辑
 *
 * <AUTHOR>
 * @date 2024/3/26
 */
@Component
public class WrapWayBeanFactory {
    private static final Logger LOGGER = LoggerFactory.getLogger(WrapWayBeanFactory.class);
    /**
     * 为减少自动适应水印字号的误差，在计算中减少页面的宽度
     */
    private static final float AUTO_RESIZE_MINUS_PAGE_REC_X = 120f;

    /**
     * 不同换行方式的处理逻辑map集合
     */
    private Map<WrapWayEnum, Function<WrapWayExecutionInDto, WrapWayExecutioOutDto>> wrapWayExecutionMaps = Maps.newHashMap();

    /**
     * 初始化OperationMaps
     **/
    @PostConstruct
    public void initApplicationModeExecutionMaps() {
        wrapWayExecutionMaps.put(WrapWayEnum.NONE, this::wrapWayNoneOperation);
        wrapWayExecutionMaps.put(WrapWayEnum.AUTO, this::wrapWayAutoOperation);
        wrapWayExecutionMaps.put(WrapWayEnum.WORD_LIMITED, this::wrapWayWordLimitedOperation);
        wrapWayExecutionMaps.put(WrapWayEnum.KEYWORD, this::wrapWayKeywordOperation);
        wrapWayExecutionMaps.put(WrapWayEnum.LINE_BREAK, this::wrapWayLineBreakOperation);
    }

    /**
     * 根据不同的换行方式执行不同的逻辑
     *
     * @param wrapWay               换行方式枚举
     * @param wrapWayExecutionInDto 换行方式执行逻辑输入dto
     * @return 换行方式执行逻辑处理后返回dto
     **/
    public WrapWayExecutioOutDto execute(WrapWayEnum wrapWay, WrapWayExecutionInDto wrapWayExecutionInDto) {
        // 从map中取出对应的函数，传入参数执行函数
        return wrapWayExecutionMaps.get(wrapWay).apply(wrapWayExecutionInDto);
    }

    /**
     * 不换行执行逻辑
     *
     * @param wrapWayExecutionInDto 换行方式执行逻辑输入dto
     */
    private WrapWayExecutioOutDto wrapWayNoneOperation(WrapWayExecutionInDto wrapWayExecutionInDto) {
        // 当前水印内容
        String content = wrapWayExecutionInDto.getWatermarkDto().getContent();
        // 判断是否需要重新改变字号
        if (Boolean.TRUE.equals(wrapWayExecutionInDto.getWatermarkDto().getAutoResize())) {
            resizeFontSize(content, wrapWayExecutionInDto);
        }
        return new WrapWayExecutioOutDto(new StringBuilder(content));
    }

    /**
     * 自动换行执行逻辑
     *
     * @param wrapWayExecutionInDto 换行方式执行逻辑输入dto
     */
    private WrapWayExecutioOutDto wrapWayAutoOperation(WrapWayExecutionInDto wrapWayExecutionInDto) {
        StringBuilder stringBuilder = new StringBuilder();
        BaseFont base = wrapWayExecutionInDto.getBase();
        float pageRectWidth = wrapWayExecutionInDto.getPageRectWidth();
        String content = wrapWayExecutionInDto.getWatermarkDto().getContent();
        Integer rotate = wrapWayExecutionInDto.getWatermarkDto().getRotate();
        Integer fontSize = wrapWayExecutionInDto.getWatermarkDto().getFontSize();
        Integer axisX = wrapWayExecutionInDto.getWatermarkDto().getAxisX();
        double realWidth = calcWidthWithRotate(content, fontSize, base, rotate);
        if (realWidth > pageRectWidth - axisX) {
            // 对水印内容进行分组
            StringBuilder lineBuilder = new StringBuilder();
            for (int i = 0; i < content.length(); i++) {
                lineBuilder.append(content.charAt(i));
                double lineWidth = calcWidthWithRotate(lineBuilder.toString(), fontSize, base, rotate);
                if (lineWidth > pageRectWidth - axisX) {
                    stringBuilder.append(WatermarkConstant.BREAK_FLAG).append(content.charAt(i));
                    lineBuilder = new StringBuilder().append(content.charAt(i));
                } else {
                    stringBuilder.append(content.charAt(i));
                }
            }
        } else {
            stringBuilder.append(content);
        }
        return new WrapWayExecutioOutDto(stringBuilder);
    }

    /**
     * 限定字数换行执行逻辑
     *
     * @param wrapWayExecutionInDto 换行方式执行逻辑输入dto
     */
    private WrapWayExecutioOutDto wrapWayWordLimitedOperation(WrapWayExecutionInDto wrapWayExecutionInDto) {
        StringBuilder stringBuilder = new StringBuilder();
        // 换行值
        int count = Integer.parseInt(wrapWayExecutionInDto.getWatermarkDto().getWrapValue());
        String content = wrapWayExecutionInDto.getWatermarkDto().getContent();
        for (int i = 0; i < content.length(); i++) {
            stringBuilder.append(content.charAt(i));
            // 字数是否换行值整数倍
            if (i != content.length() - 1 && (i + 1) % count == 0) {
                stringBuilder.append(WatermarkConstant.BREAK_FLAG);
            }
        }

        // 判断是否需要重新改变字号
        if (Boolean.TRUE.equals(wrapWayExecutionInDto.getWatermarkDto().getAutoResize())) {
            resizeFontSize(getLongestLine(wrapWayExecutionInDto, stringBuilder.toString()), wrapWayExecutionInDto);
        }
        return new WrapWayExecutioOutDto(stringBuilder);
    }

    /**
     * 关键字换行执行逻辑
     *
     * @param wrapWayExecutionInDto 换行方式执行逻辑输入dto
     */
    private WrapWayExecutioOutDto wrapWayKeywordOperation(WrapWayExecutionInDto wrapWayExecutionInDto) {
        String wrapValue = wrapWayExecutionInDto.getWatermarkDto().getWrapValue();
        String content = wrapWayExecutionInDto.getWatermarkDto().getContent();
        if (StringUtils.isNotBlank(wrapValue)) {
            List<String> wrapValues = Arrays.stream(wrapValue.split("\\^")).collect(Collectors.toList());
            for (String flag : wrapValues) {
                content = content.replaceAll(flag, StringUtils.join(WatermarkConstant.BREAK_FLAG, flag));
            }
        }

        // 判断是否需要重新改变字号
        if (Boolean.TRUE.equals(wrapWayExecutionInDto.getWatermarkDto().getAutoResize())) {
            resizeFontSize(getLongestLine(wrapWayExecutionInDto, content), wrapWayExecutionInDto);
        }
        return new WrapWayExecutioOutDto(new StringBuilder(content));
    }

    /**
     * 换行符换行执行逻辑
     */
    private WrapWayExecutioOutDto wrapWayLineBreakOperation(WrapWayExecutionInDto wrapWayExecutionInDto) {
        String content = wrapWayExecutionInDto.getWatermarkDto().getContent();
        content = content.replaceAll("@\\{ENTER}", WatermarkConstant.BREAK_FLAG);
        // 判断是否需要重新改变字号
        if (Boolean.TRUE.equals(wrapWayExecutionInDto.getWatermarkDto().getAutoResize())) {
            resizeFontSize(getLongestLine(wrapWayExecutionInDto, content), wrapWayExecutionInDto);
        }
        return new WrapWayExecutioOutDto(new StringBuilder(content));
    }

    // ********************************* 其他方法封装 ************************

    /**
     * 计算带斜率的文字宽度
     *
     * @param content  文字
     * @param fontSize 字体大小
     * @param base     字体
     * @param rotate   斜率
     * @return 宽度
     **/
    private double calcWidthWithRotate(String content, Integer fontSize, BaseFont base, int rotate) {
        // 当一个水印横向无换行放置时的宽度
        float stringWidth = base.getWidthPoint(content, fontSize);
        // 加入斜率后计算水印宽度
        return rotate != 0 ? new BigDecimal(stringWidth).multiply(BigDecimal.valueOf(Math.cos(Math.toRadians(rotate)))).doubleValue() : stringWidth;
    }

    /**
     * 水印自适应大小逻辑
     *
     * @param longestLine           水印中最长的一行内容
     * @param wrapWayExecutionInDto 换行方式执行逻辑输入dto
     */
    private void resizeFontSize(String longestLine, WrapWayExecutionInDto wrapWayExecutionInDto) {
        Integer fontSize = wrapWayExecutionInDto.getWatermarkDto().getFontSize();
        float pageRectWidth = wrapWayExecutionInDto.getPageRectWidth();
        if (!wrapWayExecutionInDto.getReCalcResizeFlag()) {
            for (int suitSize = fontSize; suitSize > 0; suitSize--) {
                double realWidth = calcWidthWithRotate(longestLine, suitSize, wrapWayExecutionInDto.getBase(), wrapWayExecutionInDto.getWatermarkDto().getRotate());
                if (realWidth <= pageRectWidth || suitSize == 1) {
                    wrapWayExecutionInDto.getWatermarkDto().setFontSize(suitSize);
                    wrapWayExecutionInDto.setReCalcResizeFlag(true);
                    LOGGER.debug("WrapWayBeanFactory wrapWayNoneOperation pageRectWidth:[{}]->pageRectWidth_offset:[{}]->realWidth:[{}]->suitSize:[{}]", pageRectWidth, pageRectWidth - AUTO_RESIZE_MINUS_PAGE_REC_X, realWidth, suitSize);
                    break;
                }
            }
        }
    }

    /**
     * 获取一组水印分行后最长的一行
     *
     * @param wrapWayExecutionInDto 换行方式执行逻辑输入dto
     * @param content               含待分行符的整行水印
     * @return 最长的一行
     **/
    private String getLongestLine(WrapWayExecutionInDto wrapWayExecutionInDto, String content) {
        Map<Double, String> linesMap = Maps.newHashMap();
        Arrays.stream(content.split(WatermarkConstant.BREAK_FLAG))
                .forEach(line -> linesMap.put(calcWidthWithRotate(line, wrapWayExecutionInDto.getWatermarkDto().getFontSize(), wrapWayExecutionInDto.getBase(), wrapWayExecutionInDto.getWatermarkDto().getRotate()), line));
        return linesMap.get(Collections.max(linesMap.keySet()));
    }
}
