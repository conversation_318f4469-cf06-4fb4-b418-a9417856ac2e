package gov.license.watermark.webApi;

import gov.license.common.api.controller.BaseController;
import gov.license.common.api.resp.BaseResponseCode;
import gov.license.common.api.resp.ResponseHelper;
import gov.license.common.api.resp.ResponseResult;
import gov.license.watermark.exception.GenerateWatermarkException;
import gov.license.watermark.req.WatermarkRequest;
import gov.license.watermark.resp.data.WatermarkPreviewRespData;
import gov.license.watermark.service.GenerateWatermarkService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Base64;

/**
 * 生成水印 应用系统控制类
 *
 * <AUTHOR>
 * @date 2023/3/24
 */
@RestController
public class GenerateWatermarkWebApi extends BaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(GenerateWatermarkWebApi.class);

    @Autowired
    private GenerateWatermarkService generateWatermarkService;

    /**
     * 水印预览（已保存模板）
     *
     * @param id 模板唯一编码
     * @date 18:47 2023/3/24
     **/
    @GetMapping("/license/webapi/v1/common/watermark/preview/{id}")
    public ResponseResult<WatermarkPreviewRespData> viewWatermarkById(@PathVariable("id") String id) {
        try {
            WatermarkPreviewRespData respData = new WatermarkPreviewRespData();
            respData.setId(id);
            respData.setBase64Data(Base64.getEncoder().encodeToString(generateWatermarkService.generateWatermark(id)));
            return ResponseHelper.success(respData);
        } catch (GenerateWatermarkException e) {
            LOGGER.error("GenerateWatermark viewWatermarkById error responseCode [{}|{}]", e.getResponseCode(),
                    e.getResponseCode().getCode(), e);
            return ResponseHelper.failure(e.getResponseCode());
        } catch (Exception e) {
            LOGGER.error("GenerateWatermark viewWatermarkById error", e);
            return ResponseHelper.failure(BaseResponseCode.SERVICE_ERROR);
        }
    }

    /**
     * 水印预览（未保存模板）
     *
     * @param request 水印样式 请求对象
     * @date 18:47 2023/3/24
     **/
    @PostMapping("/license/webapi/v1/common/watermark/preview")
    public ResponseResult<WatermarkPreviewRespData> viewWatermarkByRequest(@RequestBody WatermarkRequest request) {
        try {
            WatermarkPreviewRespData respData = new WatermarkPreviewRespData();
            respData.setBase64Data(Base64.getEncoder().encodeToString(generateWatermarkService.generateWatermark(request)));
            return ResponseHelper.success(respData);
        } catch (GenerateWatermarkException e) {
            LOGGER.error("GenerateWatermark viewWatermarkByRequest error responseCode [{}|{}]", e.getResponseCode(),
                    e.getResponseCode().getCode(), e);
            return ResponseHelper.failure(e.getResponseCode());
        } catch (Exception e) {
            LOGGER.error("GenerateWatermark viewWatermarkByRequest error", e);
            return ResponseHelper.failure(BaseResponseCode.SERVICE_ERROR);
        }
    }
}
