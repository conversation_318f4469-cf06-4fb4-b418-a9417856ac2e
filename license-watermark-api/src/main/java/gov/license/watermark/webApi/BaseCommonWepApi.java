package gov.license.watermark.webApi;

import gov.license.common.api.controller.BaseController;
import gov.license.watermark.enums.WatermarkLogTypeEnum;
import gov.license.watermark.service.WatermarkLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 水印组件公共api
 *
 * <AUTHOR>
 * @date 2024/5/14
 */
@Component
public class BaseCommonWepApi extends BaseController {

    @Autowired(required = false)
    private WatermarkLogService watermarkLogService;

    public void execLog(WatermarkLogTypeEnum watermarkLogTypeEnum) {
        if (Objects.nonNull(watermarkLogService)) {
            watermarkLogService.execLog(watermarkLogTypeEnum);
        }
    }
}
