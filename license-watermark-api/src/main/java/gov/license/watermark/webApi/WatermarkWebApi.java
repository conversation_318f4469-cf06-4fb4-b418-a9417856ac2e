package gov.license.watermark.webApi;

import gov.license.common.api.resp.BaseResponseCode;
import gov.license.common.api.resp.ResponseHelper;
import gov.license.common.api.resp.ResponseResult;
import gov.license.common.api.resp.data.BasePageRespData;
import gov.license.common.tools.bean.BeanUtil;
import gov.license.watermark.dto.WatermarkDto;
import gov.license.watermark.enums.WatermarkLogTypeEnum;
import gov.license.watermark.exception.WatermarkException;
import gov.license.watermark.req.WatermarkRequest;
import gov.license.watermark.resp.WatermarkResponseCode;
import gov.license.watermark.resp.data.WatermarkRespData;
import gov.license.watermark.service.WatermarkService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 水印样式 应用系统控制类
 *
 * <AUTHOR>
 * @date 2023/3/17
 */
@RestController
public class WatermarkWebApi extends BaseCommonWepApi {
    private static final Logger LOGGER = LoggerFactory.getLogger(WatermarkWebApi.class);

    @Autowired
    @Qualifier("watermarkServiceImpl")
    private WatermarkService watermarkService;

    /**
     * 水印样式 -> 查询列表
     *
     * @param request 水印样式 请求对象
     * @date 15:06 2023/3/17
     **/
    @PostMapping("/license/webapi/v1/common/watermark/page")
    public ResponseResult<BasePageRespData<List<WatermarkRespData>>> page(@RequestBody WatermarkRequest request) {
        try {
            BasePageRespData<List<WatermarkDto>> page = watermarkService.queryPage(request);
            return ResponseHelper.success(BeanUtil.copy(page, new BasePageRespData<>(page.getPageNum(),
                    page.getPageSize(), page.getTotalElements(), page.getTotalPages(),
                    BeanUtil.copyList(page.getContent(), WatermarkRespData.class))));
        } catch (WatermarkException e) {
            LOGGER.error("Watermark page error request:[{}]", request, e);
            LOGGER.error("Watermark page error responseCode [{}|{}]", e.getResponseCode(), e.getResponseCode().getCode(), e);
            return ResponseHelper.failure(e.getResponseCode());
        } catch (Exception e) {
            LOGGER.error("Watermark page error request:[{}]", request, e);
            return ResponseHelper.failure(BaseResponseCode.SERVICE_ERROR);
        } finally {
            execLog(WatermarkLogTypeEnum.WATERMARK_PAGE);
        }
    }

    /**
     * 水印样式 -> 查看
     *
     * @param id 唯一标识
     * @date 15:24 2023/3/17
     **/
    @GetMapping("/license/webapi/v1/common/watermark/view/{id}")
    public ResponseResult<WatermarkRespData> view(@PathVariable("id") String id) {
        try {
            WatermarkDto watermarkDto = watermarkService.getWatermarkById(id);
            return ResponseHelper.success(BeanUtil.copy(watermarkDto, WatermarkRespData.class));
        } catch (WatermarkException e) {
            LOGGER.error("Watermark view error id:[{}]", id, e);
            LOGGER.error("Watermark view error responseCode [{}|{}]", e.getResponseCode(), e.getResponseCode().getCode(), e);
            return ResponseHelper.failure(e.getResponseCode());
        } catch (Exception e) {
            LOGGER.error("Watermark view error id:[{}]", id, e);
            return ResponseHelper.failure(BaseResponseCode.SERVICE_ERROR);
        } finally {
            execLog(WatermarkLogTypeEnum.WATERMARK_VIEW);
        }
    }

    /**
     * 水印样式 -> 创建
     *
     * @param request 水印样式 请求对象
     * @date 15:31 2023/3/17
     **/
    @PostMapping("/license/webapi/v1/common/watermark/create")
    public ResponseResult<String> create(@RequestBody WatermarkRequest request) {
        try {
            watermarkService.create(request);
            return ResponseHelper.success();
        } catch (WatermarkException e) {
            LOGGER.error("Watermark create error request:[{}]", request, e);
            LOGGER.error("Watermark create error responseCode [{}|{}]", e.getResponseCode(), e.getResponseCode().getCode(), e);
            return ResponseHelper.failure(e.getResponseCode());
        } catch (Exception e) {
            LOGGER.error("Watermark create error request:[{}]", request, e);
            return ResponseHelper.failure(BaseResponseCode.SERVICE_ERROR);
        } finally {
            execLog(WatermarkLogTypeEnum.WATERMARK_CREATE);
        }
    }

    /**
     * 水印样式 -> 编辑
     *
     * @param request 水印样式 请求对象
     * @date 15:39 2023/3/17
     **/
    @PostMapping("/license/webapi/v1/common/watermark/edit")
    public ResponseResult<String> edit(@RequestBody WatermarkRequest request) {
        try {
            watermarkService.edit(request);
            return ResponseHelper.success();
        } catch (WatermarkException e) {
            LOGGER.error("Watermark edit error request:[{}]", request, e);
            LOGGER.error("Watermark edit error responseCode [{}|{}]", e.getResponseCode(), e.getResponseCode().getCode(), e);
            return ResponseHelper.failure(e.getResponseCode());
        } catch (Exception e) {
            LOGGER.error("Watermark edit error request:[{}]", request, e);
            return ResponseHelper.failure(BaseResponseCode.SERVICE_ERROR);
        } finally {
            execLog(WatermarkLogTypeEnum.WATERMARK_EDIT);
        }
    }

    /**
     * 水印样式 -> 删除（批量）
     *
     * @param requestList 水印样式 请求对象 集合
     * @date 15:58 2023/3/17
     **/
    @PostMapping("/license/webapi/v1/common/watermark/delete")
    public ResponseResult<String> delete(@RequestBody List<WatermarkRequest> requestList) {
        try {
            watermarkService.delete(requestList);
            return ResponseHelper.success();
        } catch (WatermarkException e) {
            LOGGER.error("Watermark delete error requestList:[{}]", requestList, e);
            LOGGER.error("Watermark delete error responseCode [{}|{}]", e.getResponseCode(), e.getResponseCode().getCode(), e);
            return ResponseHelper.failure(e.getResponseCode());
        } catch (Exception e) {
            LOGGER.error("Watermark delete error requestList:[{}]", requestList, e);
            return ResponseHelper.failure(BaseResponseCode.SERVICE_ERROR);
        } finally {
            execLog(WatermarkLogTypeEnum.WATERMARK_DELETE);
        }
    }

    /**
     * 查询默认水印
     **/
    @PostMapping("/license/webapi/v1/common/watermark/find_default")
    public ResponseResult<WatermarkRespData> findDefaultWatermark() {
        try {
            WatermarkDto watermarkDto = watermarkService.findDefaultWatermark().orElseThrow(() -> new WatermarkException(WatermarkResponseCode.NO_DEFAULT_WATERMARK));
            return ResponseHelper.success(BeanUtil.copy(watermarkDto, WatermarkRespData.class));
        } catch (WatermarkException e) {
            LOGGER.error("Watermark findDefaultWatermark error responseCode [{}|{}]", e.getResponseCode(), e.getResponseCode().getCode(), e);
            return ResponseHelper.failure(e.getResponseCode());
        } catch (Exception e) {
            return ResponseHelper.failure(BaseResponseCode.SERVICE_ERROR);
        }
    }

    /**
     * 设置默认水印
     *
     * @param id 水印样式id
     **/
    @PostMapping("/license/webapi/v1/common/watermark/set_default/{id}")
    public ResponseResult<String> setDefaultFlag(@PathVariable("id") String id) {
        try {
            watermarkService.setDefaultFlag(id);
            return ResponseHelper.success();
        } catch (WatermarkException e) {
            LOGGER.error("Watermark setDefaultFlag error id:[{}]", id, e);
            LOGGER.error("Watermark setDefaultFlag error responseCode [{}|{}]", e.getResponseCode(), e.getResponseCode().getCode(), e);
            return ResponseHelper.failure(e.getResponseCode());
        } catch (Exception e) {
            LOGGER.error("Watermark setDefaultFlag error id:[{}]", id, e);
            return ResponseHelper.failure(BaseResponseCode.SERVICE_ERROR);
        } finally {
            execLog(WatermarkLogTypeEnum.WATERMARK_SET_DEFAULT_FLAG);
        }
    }
}
