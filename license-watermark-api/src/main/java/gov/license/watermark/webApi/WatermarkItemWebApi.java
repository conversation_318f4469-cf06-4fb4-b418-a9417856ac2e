package gov.license.watermark.webApi;

import gov.license.common.api.resp.BaseResponseCode;
import gov.license.common.api.resp.ResponseHelper;
import gov.license.common.api.resp.ResponseResult;
import gov.license.common.tools.bean.BeanUtil;
import gov.license.watermark.dto.WatermarkItemDto;
import gov.license.watermark.enums.WatermarkLogTypeEnum;
import gov.license.watermark.exception.WatermarkException;
import gov.license.watermark.req.WatermarkItemRequest;
import gov.license.watermark.resp.data.WatermarkItemRespData;
import gov.license.watermark.service.WatermarkItemService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 水印内容信息项 应用系统控制类
 *
 * <AUTHOR>
 * @date 2023/3/17
 */
@RestController
public class WatermarkItemWebApi extends BaseCommonWepApi {
    private static final Logger LOGGER = LoggerFactory.getLogger(WatermarkItemWebApi.class);

    @Autowired
    private WatermarkItemService watermarkItemService;

    /**
     * 水印内容信息项 -> 查看列表
     *
     * @date 15:24 2023/3/17
     **/
    @GetMapping("/license/webapi/v1/common/watermark_item/list")
    public ResponseResult<List<WatermarkItemRespData>> list() {
        try {
            List<WatermarkItemDto> list = watermarkItemService.findAll();
            return ResponseHelper.success(BeanUtil.copyList(list, WatermarkItemRespData.class));
        } catch (WatermarkException e) {
            LOGGER.error("WatermarkItem list error responseCode [{}|{}]", e.getResponseCode(),
                    e.getResponseCode().getCode(), e);
            return ResponseHelper.failure(e.getResponseCode());
        } catch (Exception e) {
            LOGGER.error("WatermarkItem list error", e);
            return ResponseHelper.failure(BaseResponseCode.SERVICE_ERROR);
        } finally {
            execLog(WatermarkLogTypeEnum.WATERMARK_ITEM_LIST);
        }
    }

    /**
     * 水印内容信息项 -> 创建
     *
     * @param requestList 水印样式 请求对象集合
     * @date 15:31 2023/3/17
     **/
    @PostMapping("/license/webapi/v1/common/watermark_item/create")
    public ResponseResult<String> create(@RequestBody List<WatermarkItemRequest> requestList) {
        try {
            watermarkItemService.create(requestList);
            return ResponseHelper.success();
        } catch (WatermarkException e) {
            LOGGER.error("WatermarkItem create error requestList:[{}]", requestList, e);
            LOGGER.error("WatermarkItem create error responseCode [{}|{}]", e.getResponseCode(),
                    e.getResponseCode().getCode(), e);
            return ResponseHelper.failure(e.getResponseCode());
        } catch (Exception e) {
            LOGGER.error("WatermarkItem create error requestList:[{}]", requestList, e);
            return ResponseHelper.failure(BaseResponseCode.SERVICE_ERROR);
        } finally {
            execLog(WatermarkLogTypeEnum.WATERMARK_ITEM_CREATE);
        }
    }

    /**
     * 水印内容信息项 -> 删除（批量）
     *
     * @param requestList 水印样式 请求对象 集合
     * @date 15:58 2023/3/17
     **/
    @PostMapping("/license/webapi/v1/common/watermark_item/delete")
    public ResponseResult<String> delete(@RequestBody List<WatermarkItemRequest> requestList) {
        try {
            watermarkItemService.delete(requestList);
            return ResponseHelper.success();
        } catch (WatermarkException e) {
            LOGGER.error("WatermarkItem delete error requestList:[{}]", requestList, e);
            LOGGER.error("WatermarkItem delete error responseCode [{}|{}]", e.getResponseCode(),
                    e.getResponseCode().getCode(), e);
            return ResponseHelper.failure(e.getResponseCode());
        } catch (Exception e) {
            LOGGER.error("WatermarkItem delete error requestList:[{}]", requestList, e);
            return ResponseHelper.failure(BaseResponseCode.SERVICE_ERROR);
        } finally {
            execLog(WatermarkLogTypeEnum.WATERMARK_ITEM_DELETE);
        }
    }
}
