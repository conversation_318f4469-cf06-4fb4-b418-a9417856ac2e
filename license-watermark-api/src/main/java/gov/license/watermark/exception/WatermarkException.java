package gov.license.watermark.exception;

import gov.license.common.api.exception.ApplicationServiceException;
import gov.license.common.api.resp.BaseResponseCode;

/**
 * 水印样式异常类
 *
 * <AUTHOR>
 * @date 2023/3/17
 */
public class WatermarkException extends ApplicationServiceException {
    private static final long serialVersionUID = -7884332688761614635L;
    private final BaseResponseCode responseCode;

    public WatermarkException(BaseResponseCode responseCode, Throwable cause) {
        super(cause);
        this.responseCode = responseCode;
    }

    public WatermarkException(BaseResponseCode responseCode) {
        super(responseCode);
        this.responseCode = responseCode;
    }

    @Override
    public BaseResponseCode getResponseCode() {
        return responseCode;
    }
}
