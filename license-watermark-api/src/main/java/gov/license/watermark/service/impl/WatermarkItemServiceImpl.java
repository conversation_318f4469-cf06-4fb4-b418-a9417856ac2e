package gov.license.watermark.service.impl;

import gov.license.common.tools.bean.BeanUtil;
import gov.license.watermark.dto.WatermarkItemDto;
import gov.license.watermark.entity.WatermarkItemDo;
import gov.license.watermark.exception.WatermarkException;
import gov.license.watermark.repository.WatermarkItemRepository;
import gov.license.watermark.req.WatermarkItemRequest;
import gov.license.watermark.resp.WatermarkResponseCode;
import gov.license.watermark.service.WatermarkItemService;
import gov.license.watermark.validate.WatermarkItemRequestParamValidate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 水印内容信息项 服务类
 *
 * <AUTHOR>
 * @date 2023/3/17
 */
@Service
public class WatermarkItemServiceImpl implements WatermarkItemService {
    private static final Logger LOGGER = LoggerFactory.getLogger(WatermarkItemServiceImpl.class);

    @Autowired
    private WatermarkItemRepository watermarkItemRepository;

    @Override
    public List<WatermarkItemDto> findAll() {
        List<WatermarkItemDo> watermarkItemDos = watermarkItemRepository.findAll().stream().sorted(Comparator.comparing(WatermarkItemDo::getSort)).collect(Collectors.toList());
        return BeanUtil.copyList(watermarkItemDos, WatermarkItemDto.class);
    }

    @Override
    public WatermarkItemDto getWatermarkItemById(String id) {
        WatermarkItemDo watermarkItemDo = watermarkItemRepository.findById(id).orElseThrow(() -> new WatermarkException(WatermarkResponseCode.QUERY_LICENSE_WATERMARK_ITEM_NOT_EXIST));
        return BeanUtil.copy(watermarkItemDo, WatermarkItemDto.class);
    }

    @Override
    public void create(List<WatermarkItemRequest> request) {
        WatermarkItemRequestParamValidate.createParamValidate(request);
        watermarkItemRepository.deleteAll();

        List<WatermarkItemDo> watermarkItemDos = BeanUtil.copyList(request, WatermarkItemDo.class);
        for (int i = 0; i < watermarkItemDos.size(); i++) {
            watermarkItemDos.get(i).setSort(i + 1);
        }
        watermarkItemRepository.saveAll(watermarkItemDos);
    }

    @Override
    public void delete(List<WatermarkItemRequest> request) {
        WatermarkItemRequestParamValidate.deleteInBatchValidate(request);
        List<WatermarkItemDo> watermarkItemDos = BeanUtil.copyList(request, WatermarkItemDo.class);
        watermarkItemRepository.deleteInBatch(watermarkItemDos);
    }

}
