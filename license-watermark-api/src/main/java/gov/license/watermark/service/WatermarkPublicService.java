package gov.license.watermark.service;

import gov.license.common.api.resp.data.BasePageRespData;
import gov.license.watermark.dto.WatermarkDto;
import gov.license.watermark.entity.WatermarkDo;
import gov.license.watermark.req.WatermarkRequest;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;

import java.util.List;

/**
 * 水印样式 对外服务类
 *
 * <AUTHOR>
 * @date 2023/3/17
 */
public interface WatermarkPublicService {

    /**
     * 查询水印列表
     *
     * @param specification 查询条件
     * @param sort          排序对象
     * @date 21:03 2023/3/22
     **/
    List<WatermarkDto> findAll(Specification<WatermarkDo> specification, Sort sort);

    /**
     * 条件分页查询
     *
     * @param specification 查询明细
     * @param pageRequest   分页排序对象
     * @return 分页对象
     **/
    BasePageRespData<List<WatermarkDto>> page(Specification<WatermarkDo> specification, PageRequest pageRequest);

    /**
     * 根据唯一主键查询水印信息
     *
     * @param id 唯一主键
     * @date 16:20 2023/3/17
     **/
    WatermarkDto getWatermarkById(String id);

    /**
     * 根据水印样式名称查询水印
     *
     * @param name 水印样式名称
     * @return 水印样式对象
     **/
    WatermarkDto getWatermarkByName(String name);

    /**
     * 删除水印（支持批量）
     *
     * @param requestList 水印样式请求对象 集合
     * @date 16:29 2023/3/17
     **/
    void delete(List<WatermarkRequest> requestList);
}
