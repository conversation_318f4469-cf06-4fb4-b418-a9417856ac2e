package gov.license.watermark.service;

import gov.license.watermark.req.WatermarkItemRequest;

import java.util.List;

/**
 * 水印内容信息项 服务类
 *
 * <AUTHOR>
 * @date 2023/3/17
 */
public interface WatermarkItemService extends WatermarkItemPublicService {

    /**
     * 新增水印内容信息项
     *
     * @param requestList 水印内容信息项请求对象 集合
     * @date 16:28 2023/3/17
     **/
    void create(List<WatermarkItemRequest> requestList);

    /**
     * 删除水印内容信息项（支持批量）
     *
     * @param requestList 水印内容信息项请求对象 集合
     * @date 16:29 2023/3/17
     **/
    void delete(List<WatermarkItemRequest> requestList);

}
