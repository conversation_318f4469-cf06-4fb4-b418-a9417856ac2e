package gov.license.watermark.service;

import gov.license.watermark.dto.WatermarkDto;
import gov.license.watermark.req.WatermarkRequest;

/**
 * 生成水印预览 服务类
 *
 * <AUTHOR>
 * @date 2023/3/17
 */
public interface GenerateWatermarkService {

    /**
     * 预览水印（已保存的水印预览）
     *
     * @param id 水印唯一主键
     * @date 19:00 2023/3/20
     **/
    byte[] generateWatermark(String id) throws Exception;

    /**
     * 预览水印（未保存的水印预览）
     *
     * @param request 生成水印 请求对象
     * @date 19:00 2023/3/20
     **/
    byte[] generateWatermark(WatermarkRequest request) throws Exception;

    /**
     * 根据pdf文件和水印id生成水印
     *
     * @param pdfFile     源pdf文件
     * @param watermarkId 水印id
     * @return 生成的pdf
     **/
    byte[] generateWatermark(byte[] pdfFile, String watermarkId) throws Exception;

    /**
     * 根据pdf文件和水印dto生成水印
     *
     * @param pdfFile      源pdf文件
     * @param watermarkDto 水印id
     * @return 生成的pdf
     **/
    byte[] generateWatermark(byte[] pdfFile, WatermarkDto watermarkDto) throws Exception;
}
