package gov.license.watermark.service;

import gov.license.common.api.resp.data.BasePageRespData;
import gov.license.watermark.dto.WatermarkDto;
import gov.license.watermark.req.WatermarkRequest;

import java.util.List;
import java.util.Optional;

/**
 * 水印样式 服务类
 *
 * <AUTHOR>
 * @date 2023/3/17
 */
public interface WatermarkService extends WatermarkPublicService {

    /**
     * 查询水印列表
     *
     * @date 21:03 2023/3/22
     **/
    List<WatermarkDto> findAll();

    /**
     * 分页查询水印
     *
     * @param request 水印样式请求对象
     * @date 13:30 2023/3/17
     **/
    BasePageRespData<List<WatermarkDto>> queryPage(WatermarkRequest request);

    /**
     * 新增水印
     *
     * @param request 水印样式请求对象
     * @date 16:28 2023/3/17
     **/
    void create(WatermarkRequest request);

    /**
     * 修改水印
     *
     * @param request 水印样式请求对象
     * @date 16:29 2023/3/17
     **/
    void edit(WatermarkRequest request);

    /**
     * 设置默认水印
     *
     * @param id 水印样式id
     **/
    void setDefaultFlag(String id);

    /**
     * 查找默认水印
     *
     * @return 默认水印对象Optional
     **/
    Optional<WatermarkDto> findDefaultWatermark();
}
