package gov.license.watermark.service;

import gov.license.watermark.dto.WatermarkItemDto;

import java.util.List;

/**
 * 水印内容信息项 对外服务类
 *
 * <AUTHOR>
 * @date 2023/3/17
 */
public interface WatermarkItemPublicService {

    /**
     * 查询水印内容信息项列表
     *
     * @date 21:03 2023/3/22
     **/
    List<WatermarkItemDto> findAll();

    /**
     * 根据唯一主键查询水印内容信息项
     *
     * @param id 唯一主键
     * @date 16:20 2023/3/17
     **/
    WatermarkItemDto getWatermarkItemById(String id);
}
