package gov.license.watermark.service.impl;

import com.itextpdf.text.Rectangle;
import com.itextpdf.text.*;
import com.itextpdf.text.pdf.*;
import gov.license.common.tools.bean.BeanUtil;
import gov.license.watermark.configure.WrapWayBeanFactory;
import gov.license.watermark.constants.WatermarkConstant;
import gov.license.watermark.dto.WatermarkDto;
import gov.license.watermark.dto.WrapWayExecutionInDto;
import gov.license.watermark.entity.WatermarkDo;
import gov.license.watermark.enums.FontFamilyEnum;
import gov.license.watermark.enums.PaperDirectionEnum;
import gov.license.watermark.enums.PaperSizeEnum;
import gov.license.watermark.enums.WrapWayEnum;
import gov.license.watermark.exception.GenerateWatermarkException;
import gov.license.watermark.exception.WatermarkException;
import gov.license.watermark.repository.WatermarkRepository;
import gov.license.watermark.req.WatermarkRequest;
import gov.license.watermark.resp.WatermarkResponseCode;
import gov.license.watermark.service.GenerateWatermarkService;
import gov.license.watermark.validate.WatermarkRequestParamValidate;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import javax.swing.*;
import java.awt.*;
import java.io.*;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 生成水印预览 服务类
 *
 * <AUTHOR>
 */
@Service
public class GenerateWatermarkServiceImpl implements GenerateWatermarkService {
    private static final Logger LOGGER = LoggerFactory.getLogger(GenerateWatermarkServiceImpl.class);

    /**
     * 水印交错奇偶行处理
     */
    private static final int STAGGER_ROW_NUM = 2;
    /**
     * 水印无交错时长度
     */
    private static final float NO_STAGGER_NUM = 0f;
    /**
     * 当没有设置fontsize时，而选中自适应字号时，初始的字号
     */
    private static final Integer AUTO_RESIZE_BIGIN = 72;

    @Autowired
    private WatermarkRepository watermarkRepository;
    @Autowired
    private WrapWayBeanFactory wrapWayBeanFactory;

    @Override
    public byte[] generateWatermark(String id) throws Exception {
        WatermarkDo watermarkDo = watermarkRepository.findById(id).orElseThrow(() -> new WatermarkException(WatermarkResponseCode.QUERY_LICENSE_WATERMARK_NOT_EXIST));
        WatermarkDto watermarkDto = BeanUtil.copy(watermarkDo, WatermarkDto.class);
        LOGGER.info("generateWatermark id watermarkDto -> [{}]", watermarkDto);

        // 生成水印过程
        ByteArrayOutputStream result = getPdfWatermark(watermarkDto);
        return result.toByteArray();
    }

    @Override
    public byte[] generateWatermark(WatermarkRequest request) throws Exception {
        WatermarkRequestParamValidate.createParamValidate(request);
        WatermarkDto watermarkDto = BeanUtil.copy(request, WatermarkDto.class);
        LOGGER.info("generateWatermark request watermarkDto -> [{}]", watermarkDto);

        // 生成水印过程
        ByteArrayOutputStream result = getPdfWatermark(watermarkDto);
        return result.toByteArray();
    }

    @Override
    public byte[] generateWatermark(byte[] pdfFile, String watermarkId) throws Exception {
        WatermarkDo watermarkDo = watermarkRepository.findById(watermarkId).orElseThrow(() -> new WatermarkException(WatermarkResponseCode.QUERY_LICENSE_WATERMARK_NOT_EXIST));
        WatermarkDto watermarkDto = BeanUtil.copy(watermarkDo, WatermarkDto.class);
        LOGGER.info("generateWatermark id watermarkDto -> [{}]", watermarkDto);
        // 开始加水印
        ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(pdfFile);
        return getWatermarkPdfOutputStream(watermarkDto, byteArrayInputStream, null).toByteArray();
    }

    @Override
    public byte[] generateWatermark(byte[] pdfFile, WatermarkDto watermarkDto) throws Exception {
        // 开始加水印
        ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(pdfFile);
        return getWatermarkPdfOutputStream(watermarkDto, byteArrayInputStream, null).toByteArray();
    }

    /**
     * 根据模板参数dto对象生成pdf水印预览
     *
     * @param watermarkDto 水印信息对象
     **/
    private ByteArrayOutputStream getPdfWatermark(WatermarkDto watermarkDto) throws Exception {
        // 创建pdf文档
        String inFileName = UUID.randomUUID().toString() + ".pdf";
        String inFilePath = createDocument(inFileName, watermarkDto.getPaperSize(), watermarkDto.getPaperDirection(), watermarkDto.getPaperUrx(), watermarkDto.getPaperUry());
        LOGGER.info("GenerateWatermarkServiceImpl getPdfWatermark inFilePath:[{}]", inFilePath);
        // 开始加水印
        FileInputStream fileInputStream = new FileInputStream(inFilePath);
        return getWatermarkPdfOutputStream(watermarkDto, fileInputStream, inFilePath);
    }

    /**
     * 根据模板参数dto，pdf文件输入流，文件路径生成水印
     *
     * @param watermarkDto 模板参数dto
     * @param inputStream  pdf文件输入流
     * @param inFilePath   pdf落地文件路径
     * @return 输出流
     **/
    private ByteArrayOutputStream getWatermarkPdfOutputStream(WatermarkDto watermarkDto, InputStream inputStream, String inFilePath) throws IOException, DocumentException {
        PdfReader reader = new PdfReader(inputStream);
        ByteArrayOutputStream result = new ByteArrayOutputStream();
        PdfStamper stamper = new PdfStamper(reader, result, '\0', true);

        // 获取文字字体
        String path = new ClassPathResource(File.separator).getURL().getPath() + WatermarkConstant.FONTS_DIR + FontFamilyEnum.getFontFamilyByValue(Integer.parseInt(watermarkDto.getFontFamily())).getTtfName();
        LOGGER.info("GenerateWatermarkServiceImpl generateWatermark [{}]", path);
        BaseFont base;
        try {
            base = BaseFont.createFont(path, BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
        } catch (Exception e) {
            throw new GenerateWatermarkException(WatermarkResponseCode.GENERATE_WATERMARK_FONT_ERROR);
        }

        // 遍历pdf每一页加注水印
        try {
            setWatermarkEachPage(watermarkDto, base, stamper, reader.getNumberOfPages());
        } catch (Exception e) {
            LOGGER.error("GenerateWatermarkServiceImpl getPdfWatermark error:", e);
            throw new WatermarkException(WatermarkResponseCode.GENERATE_LICENSE_WATERMARK_ERROR, e);
        } finally {
            // 关闭资源，删除临时文件
            closeAndDelete(reader, result, stamper, inFilePath);
        }
        return result;
    }

    /**
     * 为每一页加水印
     *
     * @param watermarkDto 水印信息对象
     * @param base         字体
     * @param stamper      pdf对象
     * @param total        pdf页数
     **/
    private void setWatermarkEachPage(WatermarkDto watermarkDto, BaseFont base, PdfStamper stamper, int total) {
        Rectangle pageRect;
        PdfContentByte under;
        FontMetrics metrics;
        // 设置透明度
        PdfGState gs = new PdfGState();
        gs.setFillOpacity(1.0f - watermarkDto.getPellucidity() / 100f);
        // 每个水印字体高度·
        int textHeight;
        // 每个水印字体宽度
        int textWidth;

        // 是否平铺
        Boolean tiled = watermarkDto.getTiled();
        // 根据页面斜边自动设置倾斜角度
        Boolean autoCalcGradient = watermarkDto.getAutoCalcGradient();

        // 构造换行方式执行逻辑输入dto
        WrapWayExecutionInDto wrapWayExecutionInDto = new WrapWayExecutionInDto();
        if (Boolean.TRUE.equals(watermarkDto.getAutoResize())) {
            watermarkDto.setFontSize(AUTO_RESIZE_BIGIN);
        }
        wrapWayExecutionInDto.setWatermarkDto(watermarkDto);
        wrapWayExecutionInDto.setBase(base);
        wrapWayExecutionInDto.setWrapWay(WrapWayEnum.getWrapWayEnumByValue(Integer.parseInt(watermarkDto.getWrapWay())));

        for (int i = 1; i <= total; i++) {
            pageRect = stamper.getReader().getPageSizeWithRotation(i);
            // 在内容上方加水印
            under = stamper.getOverContent(i);
            under.saveState();
            under.restoreState();
            under.beginText();
            // 设置颜色
            Color color = Color.decode(watermarkDto.getFontColor());
            under.setColorFill(new BaseColor(color.getRed(), color.getGreen(), color.getBlue()));

            // 处理换行的水印集合
            wrapWayExecutionInDto.setPageRectWidth(pageRect.getWidth());
            List<String> wrapContontList = wrapOperation(wrapWayExecutionInDto);

            // 根据页面斜边自动设置倾斜角度
            if (Boolean.TRUE.equals(autoCalcGradient)) {
                float width = pageRect.getWidth();
                float height = pageRect.getHeight();
                // 计算弧度
                double radians = Math.atan2(height, width);
                // 将弧度转换为角度
                double degrees = Math.toDegrees(radians);
                watermarkDto.setRotate((int) degrees);
            }

            // 设置字体及字号
            under.setFontAndSize(base, watermarkDto.getFontSize());

            // 设置文字
            JLabel label = new JLabel();
            wrapContontList.forEach(label::setText);
            metrics = label.getFontMetrics(label.getFont());
            //字符串的高，和字体有关
            textHeight = metrics.getHeight();
            //字符串的宽
            textWidth = metrics.stringWidth(label.getText());
            // 设置透明度
            under.setGState(gs);
            if (Boolean.TRUE.equals(tiled)) {
                // 平铺
                isTiled(pageRect, under, textHeight, textWidth, wrapContontList, watermarkDto);
            } else {
                // 不是平铺
                isNotTiled(under, textHeight, textWidth, wrapContontList, watermarkDto);
            }
            // 添加水印文字
            under.endText();
            under.setLineWidth(1f);
            under.stroke();
        }
    }

    /**
     * 平铺
     *
     * @param pageRect        文档对象
     * @param under           pdf对象
     * @param textHeight      字符串的高
     * @param textWidth       字符串的宽
     * @param wrapContontList 水印内容集合
     * @param watermarkDto    水印对象
     **/
    private void isTiled(Rectangle pageRect, PdfContentByte under, int textHeight, int textWidth, List<String> wrapContontList, WatermarkDto watermarkDto) {
        float spacingX = Objects.nonNull(watermarkDto.getSpacingX()) ? (float) watermarkDto.getSpacingX() : 0;
        float spacingY = Objects.nonNull(watermarkDto.getSpacingY()) ? (float) watermarkDto.getSpacingY() : 0;
        float fontSize = watermarkDto.getFontSize();
        Integer rotate = watermarkDto.getRotate();
        float staggerWidth = watermarkDto.getStagger() / 100f * textWidth;
        // 是平铺，根据文档长和宽遍历输出
        int curRowNum = 1;

        for (float height = (float) watermarkDto.getAxisY(); height < pageRect.getHeight(); height = height + textHeight + spacingY) {
            for (float width = (float) watermarkDto.getAxisX() + (curRowNum % STAGGER_ROW_NUM == 1 ? staggerWidth : NO_STAGGER_NUM);
                 width < pageRect.getWidth() + textWidth;
                 width = width + textWidth + spacingX) {
                // 输出一个水印
                for (int j = 0; j < wrapContontList.size(); j++) {
                    under.showTextAligned(Element.ALIGN_LEFT, wrapContontList.get(j),
                            width,
                            height + (textHeight + fontSize) * (wrapContontList.size() - j - 1),
                            rotate);
                }
            }
            curRowNum++;
        }
    }

    /**
     * 不是平铺
     * showTextAligned的文本输入x坐标：分別为x起始坐标+每两组水印间距+每两行水印间距
     * showTextAligned的文本输入y坐标：分別为y起始坐标+每两组水印间距+每两行水印间距
     *
     * @param under           pdf对象
     * @param textHeight      字符串的高
     * @param textWidth       字符串的宽
     * @param wrapContontList 水印内容集合
     * @param watermarkDto    水印对象
     **/
    private void isNotTiled(PdfContentByte under, int textHeight, int textWidth, List<String> wrapContontList, WatermarkDto watermarkDto) {
        float spacingX = Objects.nonNull(watermarkDto.getSpacingX()) ? (float) watermarkDto.getSpacingX() : 0;
        float spacingY = Objects.nonNull(watermarkDto.getSpacingY()) ? (float) watermarkDto.getSpacingY() : 0;
        float fontSize = watermarkDto.getFontSize();
        Integer rotate = watermarkDto.getRotate();
        float staggerWidth = watermarkDto.getStagger() / 100f * textWidth;
        Integer repeatRow = watermarkDto.getRepeatRow();
        Integer repeatColumn = watermarkDto.getRepeatColumn();
        // 不是平铺，根据重复的行数和列数进行遍历输出
        for (float curRowNum = 0; curRowNum < repeatRow; curRowNum++) {
            for (float curColNum = 0; curColNum < repeatColumn; curColNum++) {
                // alignment:对齐方式，text:水印内容，x:文本输入x坐标，y:文本输入y坐标，rotation:倾斜角度
                for (int j = 0; j < wrapContontList.size(); j++) {
                    under.showTextAligned(Element.ALIGN_LEFT, wrapContontList.get(j),
                            (float) watermarkDto.getAxisX() + (textWidth + spacingX) * curColNum + (curRowNum % 2 == 1 ? staggerWidth : 0f),
                            (float) watermarkDto.getAxisY() + (textHeight + spacingY) * curRowNum + (textHeight + fontSize) * (wrapContontList.size() - j - 1),
                            rotate);
                }
            }
        }
    }

    /**
     * 根据文档尺寸，创建文档
     * (itextpdf必须落地一个真正的pdf文件，且文档内必须要有内容，才可以进行操作)
     *
     * @param inFileName     输入文件名称
     * @param paperSize      文档尺寸
     * @param paperDirection 文档方向
     * @param paperUrx       尺寸自定义宽度
     * @param paperUry       尺寸自定义高度
     **/
    private String createDocument(String inFileName, String paperSize, String paperDirection, Integer paperUrx, Integer paperUry) throws Exception {
        boolean mkdir = new File(new ClassPathResource(File.separator).getURL().getPath() + WatermarkConstant.WATERMARK_TEMPLATES_DIR).mkdir();
        if (mkdir) {
            LOGGER.info("GenerateWatermarkServiceImpl createDocument named watermark_templates_dir");
        }
        String inFilePath = new ClassPathResource(File.separator + WatermarkConstant.WATERMARK_TEMPLATES_DIR).getURL().getPath() + inFileName;
        // 文档尺寸
        PaperSizeEnum paperSizeEnum = PaperSizeEnum.getPaperSizeByValue(Integer.parseInt(paperSize));
        Rectangle rectangle = paperSizeEnum.equals(PaperSizeEnum.SELF) ? new RectangleReadOnly((float) paperUrx, (float) paperUry) : paperSizeEnum.getRectangle();
        // 文档方向
        PaperDirectionEnum paperDirec = PaperDirectionEnum.getPaperDirectionByValue(Integer.parseInt(paperDirection));
        if (PaperDirectionEnum.CROSSRANGE.equals(paperDirec)) {
            rectangle = rectangle.rotate();
        }
        // 创建文档
        Document document = new Document(rectangle);
        // 把文档写入文件
        PdfWriter writer = PdfWriter.getInstance(document, new FileOutputStream(inFilePath));
        document.open();
        // 添加内容（必须，否则报错）
        Paragraph p = new Paragraph(" ");
        document.add(p);
        document.close();
        writer.close();
        return inFilePath;
    }

    /**
     * 关闭资源
     *
     * @param reader  资源
     * @param result  资源
     * @param stamper 资源
     **/
    private void closeAndDelete(PdfReader reader, ByteArrayOutputStream result, PdfStamper stamper, String fileName) {
        if (stamper != null) {
            try {
                stamper.close();
            } catch (Exception e) {
                LOGGER.error("GenerateWatermarkServiceImpl closeAndDelete stamper close error:", e);
            }
        }
        if (reader != null) {
            try {
                reader.close();
            } catch (Exception e) {
                LOGGER.error("GenerateWatermarkServiceImpl closeAndDelete reader close error:", e);
            }
        }
        if (result != null) {
            try {
                result.close();
            } catch (Exception e) {
                LOGGER.error("GenerateWatermarkServiceImpl closeAndDelete result close error:", e);
            }
        }
        if (StringUtils.isNotBlank(fileName)) {
            File file = new File(fileName);
            if (file.exists()) {
                boolean delete = file.delete();
                LOGGER.info("GenerateWatermarkServiceImpl closeAndDelete file[{}] delete [{}]", fileName, delete);
            }
        }
    }

    /**
     * 换行处理
     *
     * @param wrapWayExecutionInDto 换行方式执行逻辑输入dto
     **/
    private List<String> wrapOperation(WrapWayExecutionInDto wrapWayExecutionInDto) {
        // 执行不同换行方式的逻辑
        StringBuilder outputContent = wrapWayBeanFactory.execute(wrapWayExecutionInDto.getWrapWay(), wrapWayExecutionInDto).getOutputContent();
        return Arrays.stream(outputContent.toString().split(WatermarkConstant.BREAK_FLAG)).collect(Collectors.toList());
    }

}
