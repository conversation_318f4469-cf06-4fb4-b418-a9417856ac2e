package gov.license.watermark.service.impl;

import com.google.common.collect.Lists;
import gov.license.common.api.resp.data.BasePageRespData;
import gov.license.common.api.utils.Assert;
import gov.license.common.tools.bean.BeanUtil;
import gov.license.jpa.Specifications;
import gov.license.watermark.dto.WatermarkDto;
import gov.license.watermark.entity.WatermarkDo;
import gov.license.watermark.exception.WatermarkException;
import gov.license.watermark.repository.WatermarkRepository;
import gov.license.watermark.req.WatermarkRequest;
import gov.license.watermark.resp.WatermarkResponseCode;
import gov.license.watermark.service.WatermarkService;
import gov.license.watermark.validate.WatermarkRequestParamValidate;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 水印样式 服务类
 *
 * <AUTHOR>
 * @date 2023/3/17
 */
@Service("watermarkServiceImpl")
public class WatermarkServiceImpl implements WatermarkService {
    private static final Logger LOGGER = LoggerFactory.getLogger(WatermarkServiceImpl.class);

    @Autowired
    protected WatermarkRepository watermarkRepository;

    @Override
    public List<WatermarkDto> findAll() {
        return BeanUtil.copyList(watermarkRepository.findAll(), WatermarkDto.class);
    }

    @Override
    public List<WatermarkDto> findAll(Specification<WatermarkDo> specification, Sort sort) {
        List<WatermarkDo> watermarkDos = watermarkRepository.findAll(specification, sort);
        return BeanUtil.copyList(watermarkDos, WatermarkDto.class);
    }

    @Override
    public BasePageRespData<List<WatermarkDto>> queryPage(WatermarkRequest request) {
        LOGGER.info("WatermarkServiceImpl queryPage request:[{}]", request);
        Specification<WatermarkDo> specification = Specifications.<WatermarkDo>and()
                .like(StringUtils.isNotBlank(request.getName()), "name", String.format("%%%s%%", request.getName()))
                .build();
        Sort sort = Sort.by(Sort.Direction.DESC, "defaultFlag", "lastModificationTime");
        PageRequest pageRequest = PageRequest.of(request.getPageNumForInt(), request.getPageSizeForInt(), sort);
        Page<WatermarkDo> page = watermarkRepository.findAll(specification, pageRequest);
        return new BasePageRespData<>(page.getNumber(), page.getSize(), page.getTotalElements(), page.getTotalPages(),
                BeanUtil.copyList(page.getContent(), WatermarkDto.class));
    }

    @Override
    public BasePageRespData<List<WatermarkDto>> page(Specification<WatermarkDo> specification, PageRequest pageRequest) {
        Page<WatermarkDo> page = watermarkRepository.findAll(specification, pageRequest);
        return new BasePageRespData<>(page.getNumber(), page.getSize(), page.getTotalElements(), page.getTotalPages(),
                BeanUtil.copyList(page.getContent(), WatermarkDto.class));
    }

    @Override
    public WatermarkDto getWatermarkById(String id) {
        WatermarkDo watermarkDo = watermarkRepository.findById(id).orElseThrow(() -> new WatermarkException(WatermarkResponseCode.QUERY_LICENSE_WATERMARK_NOT_EXIST));
        return BeanUtil.copy(watermarkDo, WatermarkDto.class);
    }

    @Override
    public WatermarkDto getWatermarkByName(String name) {
        // 新增时，逻辑中有重复校验，此处查询默认不会存在重复
        WatermarkDo watermarkDo = watermarkRepository.findByName(name).orElseThrow(() -> new WatermarkException(WatermarkResponseCode.QUERY_LICENSE_WATERMARK_NOT_EXIST));
        return BeanUtil.copy(watermarkDo, WatermarkDto.class);
    }

    @Override
    public void create(WatermarkRequest request) {
        LOGGER.info("WatermarkServiceImpl create request:[{}]", request);
        WatermarkRequestParamValidate.createParamValidate(request);
        WatermarkDo queryDo = new WatermarkDo();
        queryDo.setName(request.getName());
        boolean exists = watermarkRepository.exists(Example.of(queryDo));
        Assert.isTrue(!exists, new WatermarkException(WatermarkResponseCode.QUERY_LICENSE_WATERMARK_NAME_EXIST));
        WatermarkDo watermarkDo = BeanUtil.copy(request, WatermarkDo.class);
        watermarkRepository.save(watermarkDo);
    }

    @Override
    public void edit(WatermarkRequest request) {
        LOGGER.info("WatermarkServiceImpl edit request:[{}]", request);
        WatermarkRequestParamValidate.createParamValidate(request);
        WatermarkDo watermarkDo = watermarkRepository.findById(request.getId()).orElseThrow(() -> new WatermarkException(WatermarkResponseCode.QUERY_LICENSE_WATERMARK_NOT_EXIST));

        watermarkDo.setName(request.getName());
        watermarkDo.setDescription(request.getDescription());
        watermarkDo.setContent(request.getContent());
        watermarkDo.setWrapWay(request.getWrapWay());
        watermarkDo.setWrapValue(request.getWrapValue());
        watermarkDo.setFontFamily(request.getFontFamily());
        watermarkDo.setFontColor(request.getFontColor());
        watermarkDo.setFontSize(request.getFontSize());
        watermarkDo.setRotate(request.getRotate());
        watermarkDo.setPellucidity(request.getPellucidity());
        watermarkDo.setAxisX(request.getAxisX());
        watermarkDo.setAxisY(request.getAxisY());
        watermarkDo.setRepeatRow(request.getRepeatRow());
        watermarkDo.setRepeatColumn(request.getRepeatColumn());
        watermarkDo.setTiled(request.getTiled());
        watermarkDo.setSpacingX(request.getSpacingX());
        watermarkDo.setSpacingY(request.getSpacingY());
        watermarkDo.setStagger(request.getStagger());
        watermarkDo.setPaperSize(request.getPaperSize());
        watermarkDo.setPaperDirection(request.getPaperDirection());
        watermarkDo.setDefaultFlag(request.getDefaultFlag());
        watermarkRepository.save(watermarkDo);
    }

    @Override
    @Transactional(rollbackFor = {Exception.class, Error.class})
    public void delete(List<WatermarkRequest> request) {
        LOGGER.info("WatermarkServiceImpl delete request:[{}]", request);
        WatermarkRequestParamValidate.deleteInBatchValidate(request);
        List<WatermarkDo> watermarkDos = request.stream().map(watermarkRequest -> {
            Assert.hasText(watermarkRequest.getId(), new WatermarkException(WatermarkResponseCode.DELETE_LICENSE_WATERMARK_ID_NULL));
            WatermarkDo watermarkDo = watermarkRepository.findById(watermarkRequest.getId()).orElseThrow(() -> new WatermarkException(WatermarkResponseCode.QUERY_LICENSE_WATERMARK_NOT_EXIST));
            Assert.isTrue(!watermarkDo.getDefaultFlag(), new WatermarkException(WatermarkResponseCode.DEFAULT_LICENSE_WATERMARK_CANNOT_DELETE));
            return watermarkDo;
        }).collect(Collectors.toList());
        watermarkRepository.deleteInBatch(watermarkDos);
    }

    @Override
    @Transactional(rollbackFor = {Exception.class, Error.class})
    public void setDefaultFlag(String id) {
        Assert.hasText(id, new WatermarkException(WatermarkResponseCode.DELETE_LICENSE_WATERMARK_ID_NULL));
        ArrayList<WatermarkDo> watermarkDos = Lists.newArrayList();
        // 替换原来的默认水印
        findDefaultWatermark().ifPresent(watermarkDto -> {
            WatermarkDo defaultWatermark = BeanUtil.copy(watermarkDto, WatermarkDo.class);
            defaultWatermark.setDefaultFlag(false);
            watermarkDos.add(defaultWatermark);
        });
        WatermarkDo watermarkDo = watermarkRepository.findById(id).orElseThrow(() -> new WatermarkException(WatermarkResponseCode.QUERY_LICENSE_WATERMARK_NOT_EXIST));
        if (Boolean.FALSE.equals(watermarkDo.getDefaultFlag())) {
            watermarkDo.setDefaultFlag(true);
            watermarkDos.add(watermarkDo);
        }
        watermarkRepository.saveAll(watermarkDos);
    }

    @Override
    public Optional<WatermarkDto> findDefaultWatermark() {
        Specification<WatermarkDo> specification = Specifications.<WatermarkDo>and().eq("defaultFlag", true).build();
        List<WatermarkDo> watermarkDos = watermarkRepository.findAll(specification);
        Assert.isTrue(watermarkDos.size() <= 1, new WatermarkException(WatermarkResponseCode.QUERY_DEFAULT_WATERMARK_COUNT_ERROR));
        return watermarkDos.size() == 1 ? Optional.ofNullable(BeanUtil.copy(watermarkDos.get(0), WatermarkDto.class)) : Optional.empty();
    }
}
