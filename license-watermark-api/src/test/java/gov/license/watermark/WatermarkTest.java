package gov.license.watermark;

import gov.license.watermark.exception.WatermarkException;
import gov.license.watermark.resp.WatermarkResponseCode;
import gov.license.watermark.service.GenerateWatermarkService;
import org.aspectj.util.FileUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.io.FileOutputStream;

/**
 * 水印测试类
 *
 * <AUTHOR>
 * @date 2023/3/22
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class WatermarkTest {
    private static final Logger LOGGER = LoggerFactory.getLogger(WatermarkTest.class);

    @Autowired
    private GenerateWatermarkService generateWatermarkService;

    @Test
    public void generateWatermark() {
        try {
//            byte[] bytes = generateWatermarkService.generateWatermark("2");
            byte[] bytes = generateWatermarkService.generateWatermark(FileUtil.readAsByteArray(new File("D:\\平台数据事业部质量管理工作规范V1.0版.pdf")), "2");
            FileOutputStream fos = new FileOutputStream("D:\\a.pdf");
            fos.write(bytes);
            fos.close();
        } catch (Exception e) {
            throw new WatermarkException(WatermarkResponseCode.GENERATE_LICENSE_WATERMARK_ERROR, e);
        }
    }

}
