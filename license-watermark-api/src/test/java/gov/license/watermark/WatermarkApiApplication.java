package gov.license.watermark;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;

/**
 * 应用入口
 *
 * <AUTHOR>
 */
@SpringBootApplication
@ServletComponentScan
@EnableJpaAuditing
public class WatermarkApiApplication {

    public static void main(String[] args) {
        SpringApplication.run(WatermarkApiApplication.class, args);
    }

}
