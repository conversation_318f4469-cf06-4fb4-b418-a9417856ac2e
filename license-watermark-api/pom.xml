<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>gov.license</groupId>
        <artifactId>license-parent</artifactId>
        <version>*******-SNAPSHOT</version>
        <relativePath/>
    </parent>
    <groupId>gov.license.watermark</groupId>
    <artifactId>license-watermark-api</artifactId>
    <version>*******-SNAPSHOT</version>
    <packaging>jar</packaging>
    <name>${project.artifactId}</name>
    <description>本项目作为后端(接口)系统;提供水印样式配置功能</description>
    <properties>
        <license-common.version>*******-SNAPSHOT</license-common.version>
        <licc-base.version>*******-SNAPSHOT</licc-base.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>gov.license</groupId>
            <artifactId>licc-base</artifactId>
            <version>${licc-base.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>gov.license</groupId>
            <artifactId>license-common-jpa-spec</artifactId>
            <version>${license-common.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itextpdf</artifactId>
            <version>********</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.kingbase</groupId>
            <artifactId>kingbase8</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>
</project>